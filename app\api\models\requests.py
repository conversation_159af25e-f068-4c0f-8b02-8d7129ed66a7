"""
API 请求数据模型

定义所有 API 端点的请求数据结构
"""

from typing import Optional, List, Dict, Any
from pydantic import BaseModel, Field, validator
from enum import Enum


class DocumentFormat(str, Enum):
    """支持的文档格式"""
    DOCX = "docx"
    XLSX = "xlsx" 
    XLS = "xls"
    CSV = "csv"
    TXT = "txt"
    MARKDOWN = "md"


class QueryLanguage(str, Enum):
    """查询语言类型"""
    CHINESE = "zh"
    ENGLISH = "en"
    AUTO = "auto"


class ExtractionMode(str, Enum):
    """信息提取模式"""
    SIMPLE = "simple"      # 简单提取
    DETAILED = "detailed"  # 详细提取
    STRUCTURED = "structured"  # 结构化提取
    RAW = "raw"           # 原始文本提取


class DocumentUploadRequest(BaseModel):
    """文档上传请求模型"""
    filename: str = Field(..., description="文件名")
    content_type: str = Field(..., description="文件MIME类型")
    
    @validator('filename')
    def validate_filename(cls, v):
        if not v or len(v.strip()) == 0:
            raise ValueError("文件名不能为空")
        return v.strip()


class TextQueryRequest(BaseModel):
    """直接文本查询请求模型"""
    text: str = Field(..., min_length=1, max_length=50000, description="要分析的文本内容")
    query: str = Field(..., min_length=1, max_length=1000, description="自然语言查询")
    language: QueryLanguage = Field(default=QueryLanguage.AUTO, description="查询语言")
    extraction_mode: ExtractionMode = Field(default=ExtractionMode.SIMPLE, description="提取模式")
    # RAW模式分块配置（仅在extraction_mode为RAW时生效）
    chunk_size: Optional[int] = Field(default=None, gt=0, description="RAW模式分块大小（字符数），仅在RAW模式下生效，为空时使用系统默认值65535")
    overlap_ratio: Optional[float] = Field(default=None, ge=0.0, le=0.5, description="分块重叠比例（0.0-0.5），仅在RAW模式下生效，为空时使用系统默认值0.1")
    enable_deduplication: Optional[bool] = Field(default=None, description="是否启用结果去重，仅在RAW模式下生效，为空时使用系统默认值True")
    
    @validator('text')
    def validate_text(cls, v):
        if not v or len(v.strip()) == 0:
            raise ValueError("文本内容不能为空")
        return v.strip()
    
    @validator('query')
    def validate_query(cls, v):
        if not v or len(v.strip()) == 0:
            raise ValueError("查询内容不能为空")
        return v.strip()


class DocumentQueryRequest(BaseModel):
    """基于文档的查询请求模型"""
    document_id: str = Field(..., description="文档ID")
    query: str = Field(..., min_length=1, max_length=1000, description="自然语言查询")
    language: QueryLanguage = Field(default=QueryLanguage.AUTO, description="查询语言")
    extraction_mode: ExtractionMode = Field(default=ExtractionMode.SIMPLE, description="提取模式")
    # RAW模式分块配置（仅在extraction_mode为RAW时生效）
    chunk_size: Optional[int] = Field(default=None, gt=0, description="RAW模式分块大小（字符数），仅在RAW模式下生效，为空时使用系统默认值65535")
    overlap_ratio: Optional[float] = Field(default=None, ge=0.0, le=0.5, description="分块重叠比例（0.0-0.5），仅在RAW模式下生效，为空时使用系统默认值0.1")
    enable_deduplication: Optional[bool] = Field(default=None, description="是否启用结果去重，仅在RAW模式下生效，为空时使用系统默认值True")
    
    @validator('document_id')
    def validate_document_id(cls, v):
        if not v or len(v.strip()) == 0:
            raise ValueError("文档ID不能为空")
        return v.strip()
    
    @validator('query')
    def validate_query(cls, v):
        if not v or len(v.strip()) == 0:
            raise ValueError("查询内容不能为空")
        return v.strip()


class BatchQueryRequest(BaseModel):
    """批量查询请求模型"""
    queries: List[str] = Field(..., min_items=1, max_items=10, description="查询列表")
    document_id: Optional[str] = Field(None, description="文档ID（可选，用于文档查询）")
    text: Optional[str] = Field(None, description="文本内容（可选，用于文本查询）")
    language: QueryLanguage = Field(default=QueryLanguage.AUTO, description="查询语言")
    extraction_mode: ExtractionMode = Field(default=ExtractionMode.SIMPLE, description="提取模式")
    # RAW模式分块配置（仅在extraction_mode为RAW时生效）
    chunk_size: Optional[int] = Field(default=None, gt=0, description="RAW模式分块大小（字符数），仅在RAW模式下生效，为空时使用系统默认值65535")
    overlap_ratio: Optional[float] = Field(default=None, ge=0.0, le=0.5, description="分块重叠比例（0.0-0.5），仅在RAW模式下生效，为空时使用系统默认值0.1")
    enable_deduplication: Optional[bool] = Field(default=None, description="是否启用结果去重，仅在RAW模式下生效，为空时使用系统默认值True")
    
    @validator('queries')
    def validate_queries(cls, v):
        if not v:
            raise ValueError("查询列表不能为空")
        cleaned_queries = []
        for query in v:
            if query and query.strip():
                cleaned_queries.append(query.strip())
        if not cleaned_queries:
            raise ValueError("至少需要一个有效的查询")
        return cleaned_queries
    
    @validator('text')
    def validate_text(cls, v):
        if v is not None and len(v.strip()) == 0:
            raise ValueError("如果提供文本内容，不能为空")
        return v.strip() if v else None


class PipelineConfigRequest(BaseModel):
    """管道配置请求模型"""
    model_name: Optional[str] = Field(default=None, description="LLM模型名称")
    temperature: Optional[float] = Field(default=0.3, ge=0.0, le=1.0, description="温度参数")
    max_tokens: Optional[int] = Field(default=4096, gt=0, description="最大令牌数")
    use_concurrency: bool = Field(default=False, description="是否使用并发处理")
    custom_instructions: Optional[str] = Field(default=None, description="自定义指令")
    
    @validator('custom_instructions')
    def validate_custom_instructions(cls, v):
        if v is not None and len(v.strip()) == 0:
            return None
        return v.strip() if v else None
