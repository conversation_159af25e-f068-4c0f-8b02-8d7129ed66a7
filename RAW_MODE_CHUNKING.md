# RAW模式分块处理功能

## 概述

本功能为ExtracInfo系统的RAW模式添加了智能分块处理能力，解决了长文本在LLM处理时可能遇到的上下文窗口限制问题。当文本长度超过指定阈值时，系统会自动将文本分割成多个块，并发处理后合并结果。

## 功能特性

### 1. 智能文本分割
- **句子边界分割**: 优先在句子结束符（。！？.!?）处分割，保持语义完整性
- **重叠处理**: 支持块间重叠，确保上下文连续性
- **自适应分割**: 当单个段落过长时，自动按句子进一步分割

### 2. 并发处理
- **异步处理**: 所有文本块并发处理，提高处理效率
- **错误隔离**: 单个块处理失败不影响其他块
- **进度跟踪**: 详细的处理日志和进度信息

### 3. 智能合并
- **去重处理**: 基于内容相似度的智能去重算法
- **结果合并**: 相关的文本块结果智能合并
- **元数据保留**: 保留块索引、处理时间等元数据信息

## 配置参数

### 系统默认配置
在 `app/core/config.py` 中设置：

```python
# RAW 模式分块处理配置
raw_text_chunk_size: int = 65535  # 分块大小（字符数）
raw_text_overlap_ratio: float = 0.1  # 重叠比例（10%）
enable_chunk_deduplication: bool = True  # 启用去重
```

### API参数配置
所有查询API都支持以下可选参数（仅在RAW模式下生效）：

- `chunk_size`: 分块大小（字符数），默认65535
- `overlap_ratio`: 重叠比例（0.0-0.5），默认0.1
- `enable_deduplication`: 是否启用去重，默认True

## API使用示例

### 1. 文本查询（使用默认配置）

```json
{
  "text": "很长的文本内容...",
  "query": "总结主要内容",
  "language": "zh",
  "extraction_mode": "raw"
}
```

### 2. 文本查询（自定义分块配置）

```json
{
  "text": "很长的文本内容...",
  "query": "提取关键信息",
  "language": "zh",
  "extraction_mode": "raw",
  "chunk_size": 32768,
  "overlap_ratio": 0.15,
  "enable_deduplication": true
}
```

### 3. 文档查询

```json
{
  "document_id": "doc_123",
  "query": "分析文档内容",
  "language": "zh",
  "extraction_mode": "raw",
  "chunk_size": 40000,
  "overlap_ratio": 0.2
}
```

### 4. 批量查询

```json
{
  "text": "长文本内容...",
  "queries": [
    "提取主要观点",
    "总结关键信息",
    "分析重要数据"
  ],
  "language": "zh",
  "extraction_mode": "raw",
  "chunk_size": 30000
}
```

## 处理流程

1. **文本长度检查**: 检查文本是否超过配置的分块大小阈值
2. **智能分割**: 如果超过阈值，按配置参数进行智能分割
3. **并发处理**: 所有文本块并发提交给LLM处理
4. **结果收集**: 收集所有块的处理结果
5. **去重处理**: 根据配置进行内容去重
6. **智能合并**: 合并相关的结果项
7. **返回结果**: 返回完整的处理结果

## 性能优化

### 1. 分块大小建议
- **小文档（<10K字符）**: 不分块，直接处理
- **中等文档（10K-50K字符）**: chunk_size=32768
- **大文档（50K-200K字符）**: chunk_size=65535
- **超大文档（>200K字符）**: chunk_size=65535，overlap_ratio=0.05

### 2. 重叠比例建议
- **技术文档**: 0.1-0.15（保持术语连续性）
- **小说文本**: 0.05-0.1（减少重复内容）
- **学术论文**: 0.15-0.2（保持逻辑连续性）

### 3. 去重配置建议
- **信息提取任务**: 启用去重（避免重复信息）
- **创意写作任务**: 禁用去重（保持所有创意内容）
- **数据分析任务**: 启用去重（避免重复统计）

## 注意事项

1. **仅RAW模式生效**: 分块配置参数只在`extraction_mode="raw"`时生效
2. **内存使用**: 大文档分块处理会增加内存使用，建议监控系统资源
3. **处理时间**: 分块处理可能增加总处理时间，但提高了成功率
4. **并发限制**: 系统会根据配置限制并发数量，避免资源过载

## 错误处理

- **单块失败**: 单个文本块处理失败不会影响其他块
- **超时处理**: 每个块都有独立的超时设置
- **回退机制**: 如果分块处理失败，会尝试更小的分块大小
- **错误日志**: 详细的错误日志帮助诊断问题

## 监控和日志

系统提供详细的处理日志：

```
INFO: 文本长度(150000)超过阈值(65535)，使用分块处理
INFO: 文本分割完成，共3个块
INFO: 处理块 1/3，长度: 65535
INFO: 处理块 2/3，长度: 65535  
INFO: 处理块 3/3，长度: 19000
INFO: 所有块处理完成，共获得8个结果项
INFO: 去重处理: 8 -> 6
INFO: 结果合并: 6 -> 3
```

## 测试

使用提供的测试脚本验证功能：

```bash
# 单元测试
python test_chunking.py

# API测试
python example_chunking_api.py
```
