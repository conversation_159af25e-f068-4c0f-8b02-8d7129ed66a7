"""
基于 ContextGem 官方文档的查询处理器

参考: https://contextgem.dev/quickstart.html
"""

import os
import time
import asyncio
from typing import List, Optional, Dict, Any
from aiolimiter import AsyncLimiter
from contextgem import (
    Document,
    DocumentLLM,
    Aspect,
    StringConcept,
    BooleanConcept,
    JsonObjectConcept,
    StringExample
)

from app.core.config import settings
from app.core.exceptions import QueryProcessingError, ContextGemError
from app.api.models.requests import ExtractionMode, QueryLanguage
from app.api.models.responses import ExtractedItem, ExtractionResult
from app.utils.logger import app_logger


class ContextGemQueryProcessor:
    """基于 ContextGem 官方文档的查询处理器"""
    
    def __init__(self):
        """初始化查询处理器"""
        self.llm = None
        self._initialize_llm()
    
    def _initialize_llm(self) -> None:
        """初始化 ContextGem DocumentLLM"""
        try:
            if not settings.openai_api_key:
                raise ContextGemError("未配置 OpenAI API 密钥")
            
            # 设置环境变量（ContextGem 要求）
            os.environ["CONTEXTGEM_OPENAI_API_KEY"] = settings.openai_api_key
            if settings.openai_base_url:
                os.environ["OPENAI_API_BASE"] = settings.openai_base_url
            
            # 确保模型名称格式正确
            model_name = settings.openai_model
            if not model_name.startswith("openai/"):
                model_name = f"openai/{model_name}"
            
            # 创建 DocumentLLM 实例
            llm_config = {
                "model": model_name,
                "api_key": settings.openai_api_key,
                "role": "extractor_text",
                "temperature": 0.3,
                "max_tokens": 4096
            }

            # 如果配置了自定义 api_base，添加到配置中
            if settings.openai_base_url:
                llm_config["api_base"] = settings.openai_base_url

            # 尝试添加 AsyncLimiter 支持并发处理
            try:
                llm_config["async_limiter"] = AsyncLimiter(10, 5)  # 每5秒最多10个请求
                app_logger.info("启用 AsyncLimiter 支持并发处理")
            except Exception as e:
                app_logger.warning(f"AsyncLimiter 配置失败，使用默认配置: {e}")

            self.llm = DocumentLLM(**llm_config)
            
            app_logger.info(f"ContextGem DocumentLLM 初始化成功: {model_name}")
            
        except Exception as e:
            app_logger.error(f"ContextGem DocumentLLM 初始化失败: {str(e)}")
            raise ContextGemError(f"DocumentLLM 初始化失败: {str(e)}")
    
    async def process_text_query(
        self,
        text: str,
        query: str,
        language: QueryLanguage = QueryLanguage.AUTO,
        extraction_mode: ExtractionMode = ExtractionMode.SIMPLE
    ) -> ExtractionResult:
        """
        处理文本查询 - 基于官方文档的概念提取模式
        
        Args:
            text: 要查询的文本内容
            query: 自然语言查询
            language: 查询语言
            extraction_mode: 提取模式
            
        Returns:
            ExtractionResult: 提取结果
        """
        start_time = time.time()
        
        try:
            app_logger.info(f"开始处理文本查询: {query[:50]}...")

            # 检查是否为 RAW 模式
            if extraction_mode == ExtractionMode.RAW:
                app_logger.info("使用 RAW 模式进行原始文本提取")
                # 使用专门的原始文本提取方法
                return await self.process_raw_text_extraction(text, query, language)

            # 1. 创建 Document 实例
            doc = Document(raw_text=text)

            # 2. 根据提取模式定义概念
            concepts = self._create_concepts_for_query(query, extraction_mode)

            # 3. 添加概念到文档
            doc.add_concepts(concepts)

            # 4. 使用 DocumentLLM 进行提取
            extracted_concepts = self.llm.extract_concepts_from_document(doc)

            # 5. 转换结果格式
            extracted_items = self._convert_concepts_to_items(extracted_concepts, query)

            # 生成摘要
            summary = self._generate_summary(extracted_items, query)

            processing_time = time.time() - start_time

            app_logger.info(f"文本查询处理完成，耗时: {processing_time:.2f}秒")

            return ExtractionResult(
                query=query,
                extracted_items=extracted_items,
                summary=summary,
                processing_time=processing_time,
                token_usage={
                    "prompt_tokens": 0,
                    "completion_tokens": 0,
                    "total_tokens": 0
                }
            )
            
        except Exception as e:
            app_logger.error(f"文本查询处理失败: {str(e)}")
            raise QueryProcessingError(f"查询处理失败: {str(e)}")
    
    async def process_document_query(
        self,
        contextgem_doc: Document,
        query: str,
        language: QueryLanguage = QueryLanguage.AUTO,
        extraction_mode: ExtractionMode = ExtractionMode.SIMPLE
    ) -> ExtractionResult:
        """
        处理基于 ContextGem 文档的查询 - 基于官方文档的方面提取模式
        
        Args:
            contextgem_doc: ContextGem 文档对象
            query: 自然语言查询
            language: 查询语言
            extraction_mode: 提取模式
            
        Returns:
            ExtractionResult: 提取结果
        """
        start_time = time.time()
        
        try:
            app_logger.info(f"开始处理 ContextGem 文档查询: {query[:50]}...")

            # 检查是否为 RAW 模式
            if extraction_mode == ExtractionMode.RAW:
                # 对于 RAW 模式，直接从文档原始文本中提取
                return await self.process_raw_text_extraction(contextgem_doc.raw_text, query, language)

            # 1. 定义方面（Aspect）
            aspect = self._create_aspect_for_query(query, extraction_mode)

            # 2. 添加方面到文档
            contextgem_doc.add_aspects([aspect])

            # 3. 使用 DocumentLLM 进行提取
            extracted_aspects = self.llm.extract_aspects_from_document(contextgem_doc)

            # 4. 转换结果格式
            extracted_items = self._convert_aspects_to_items(extracted_aspects, query)

            # 生成摘要
            summary = self._generate_summary(extracted_items, query)

            processing_time = time.time() - start_time

            app_logger.info(f"ContextGem 文档查询处理完成，耗时: {processing_time:.2f}秒")

            return ExtractionResult(
                query=query,
                extracted_items=extracted_items,
                summary=summary,
                processing_time=processing_time,
                token_usage={
                    "prompt_tokens": 0,
                    "completion_tokens": 0,
                    "total_tokens": 0
                }
            )
            
        except Exception as e:
            app_logger.error(f"ContextGem 文档查询处理失败: {str(e)}")
            raise QueryProcessingError(f"查询处理失败: {str(e)}")
    
    def _create_concepts_for_query(self, query: str, extraction_mode: ExtractionMode) -> List:
        """
        根据查询创建概念 - 基于官方文档的概念提取模式

        Args:
            query: 用户查询
            extraction_mode: 提取模式

        Returns:
            List: 概念列表
        """
        concepts = []

        # 检测是否为表格数据查询
        is_tabular_query = self._is_tabular_query(query)

        if extraction_mode == ExtractionMode.SIMPLE:
            # 简单模式：使用 StringConcept
            if is_tabular_query:
                concept = StringConcept(
                    name="表格数据查询结果",
                    description=f"根据查询'{query}'从表格数据中提取的相关信息，包括列名、数据值和统计信息",
                    examples=[
                        StringExample(content="列名: 姓名, 年龄, 部门\n数据: 张三: 25岁, 销售部")
                    ],
                    add_references=True,
                    reference_depth="sentences"
                )
            else:
                concept = StringConcept(
                    name="查询结果",
                    description=f"根据查询'{query}'提取的相关信息",
                    examples=[
                        StringExample(content="相关信息的示例格式")
                    ],
                    add_references=True,
                    reference_depth="sentences"
                )
            concepts.append(concept)

        elif extraction_mode == ExtractionMode.DETAILED:
            # 详细模式：使用多个 StringConcept 和 BooleanConcept
            if is_tabular_query:
                concepts.extend([
                    StringConcept(
                        name="表格结构信息",
                        description=f"表格的列名、数据类型和基本结构信息",
                        add_references=True,
                        reference_depth="sentences"
                    ),
                    StringConcept(
                        name="数据内容",
                        description=f"与查询'{query}'相关的具体数据行和值",
                        add_references=True,
                        reference_depth="sentences"
                    ),
                    StringConcept(
                        name="统计分析",
                        description=f"相关数据的统计信息，如平均值、最大值、最小值等",
                        add_references=True,
                        reference_depth="sentences"
                    ),
                    BooleanConcept(
                        name="数据完整性",
                        description="提取的表格数据是否完整和准确",
                        add_justifications=True
                    )
                ])
            else:
                concepts.extend([
                    StringConcept(
                        name="主要信息",
                        description=f"根据查询'{query}'提取的主要信息",
                        add_references=True,
                        reference_depth="sentences"
                    ),
                    StringConcept(
                        name="详细描述",
                        description=f"与查询'{query}'相关的详细描述和说明",
                        add_references=True,
                        reference_depth="sentences"
                    ),
                    BooleanConcept(
                        name="信息完整性",
                        description="提取的信息是否完整和充分",
                        add_justifications=True
                    )
                ])

        elif extraction_mode == ExtractionMode.RAW:
            # 原始文本模式：提取所有相关的原始文本内容
            concept = StringConcept(
                name="原始文本内容",
                description=f"与查询'{query}'相关的所有原始文本内容，保持原始格式",
                examples=[
                    StringExample(content="保持原始格式的文本内容")
                ],
                add_references=True,
                reference_depth="paragraphs"  # 使用段落级别获取更多上下文
            )
            concepts.append(concept)

        else:  # STRUCTURED
            # 结构化模式：使用 JsonObjectConcept
            if is_tabular_query:
                concept = JsonObjectConcept(
                    name="结构化表格数据",
                    description=f"根据查询'{query}'提取的结构化表格信息",
                    structure={
                        "column_names": List[str],
                        "data_rows": List[Dict[str, str]],
                        "statistics": Dict[str, str],
                        "summary": str
                    },
                    add_references=True,
                    reference_depth="paragraphs"
                )
            else:
                concept = JsonObjectConcept(
                    name="结构化信息",
                    description=f"根据查询'{query}'提取的结构化信息",
                    structure={
                        "main_points": List[str],
                        "details": str,
                        "source_context": str
                    },
                    add_references=True,
                    reference_depth="paragraphs"
                )
            concepts.append(concept)

        return concepts

    def _is_tabular_query(self, query: str) -> bool:
        """
        检测查询是否针对表格数据

        Args:
            query: 用户查询

        Returns:
            bool: 是否为表格数据查询
        """
        # 表格相关关键词
        tabular_keywords = [
            # 中文关键词
            '列', '行', '表格', '数据', '统计', '平均', '最大', '最小', '总和', '计算',
            '字段', '记录', '条目', '项目', '汇总', '分组', '排序', '筛选', '过滤',
            '工作表', 'sheet', '表单', '清单', '名单', '列表',
            # 英文关键词
            'column', 'row', 'table', 'data', 'statistics', 'average', 'max', 'min', 'sum', 'calculate',
            'field', 'record', 'entry', 'item', 'summary', 'group', 'sort', 'filter',
            'worksheet', 'spreadsheet', 'list'
        ]

        query_lower = query.lower()
        return any(keyword in query_lower for keyword in tabular_keywords)

    def _create_aspect_for_query(self, query: str, extraction_mode: ExtractionMode) -> Aspect:
        """
        根据查询创建方面 - 基于官方文档的方面提取模式

        Args:
            query: 用户查询
            extraction_mode: 提取模式

        Returns:
            Aspect: 方面对象
        """
        # 根据提取模式设置不同的参考深度
        if extraction_mode == ExtractionMode.RAW:
            reference_depth = "paragraphs"  # RAW 模式使用段落级别
        else:
            reference_depth = "sentences"   # 其他模式使用句子级别

        # 创建主要方面
        aspect = Aspect(
            name="查询相关内容",
            description=f"与查询'{query}'相关的文档内容",
            reference_depth=reference_depth
        )

        # 根据提取模式添加概念到方面
        concepts = self._create_concepts_for_query(query, extraction_mode)
        if concepts:
            aspect.add_concepts(concepts)

        return aspect
    
    def _convert_concepts_to_items(self, extracted_concepts: List, query: str) -> List[ExtractedItem]:
        """
        将 ContextGem 概念提取结果转换为 ExtractedItem
        
        Args:
            extracted_concepts: ContextGem 提取的概念列表
            query: 原始查询
            
        Returns:
            List[ExtractedItem]: 提取的信息项列表
        """
        extracted_items = []
        
        for concept in extracted_concepts:
            if hasattr(concept, 'extracted_items') and concept.extracted_items:
                for item in concept.extracted_items:
                    extracted_items.append(ExtractedItem(
                        content=str(item.value) if hasattr(item, 'value') else str(item),
                        confidence=getattr(item, 'confidence', None),
                        source_reference=getattr(item, 'reference', None),
                        metadata={
                            "type": "concept_extraction",
                            "concept_name": concept.name,
                            "concept_description": concept.description,
                            "extraction_method": "contextgem_concept"
                        }
                    ))
        
        return extracted_items
    
    def _convert_aspects_to_items(self, extracted_aspects: List, query: str) -> List[ExtractedItem]:
        """
        将 ContextGem 方面提取结果转换为 ExtractedItem
        
        Args:
            extracted_aspects: ContextGem 提取的方面列表
            query: 原始查询
            
        Returns:
            List[ExtractedItem]: 提取的信息项列表
        """
        extracted_items = []
        
        for aspect in extracted_aspects:
            # 处理方面级别的提取项
            if hasattr(aspect, 'extracted_items') and aspect.extracted_items:
                for item in aspect.extracted_items:
                    extracted_items.append(ExtractedItem(
                        content=str(item.value) if hasattr(item, 'value') else str(item),
                        confidence=getattr(item, 'confidence', None),
                        source_reference=getattr(item, 'reference', None),
                        metadata={
                            "type": "aspect_extraction",
                            "aspect_name": aspect.name,
                            "aspect_description": aspect.description,
                            "extraction_method": "contextgem_aspect"
                        }
                    ))
            
            # 处理方面中的概念提取项
            if hasattr(aspect, 'concepts'):
                for concept in aspect.concepts:
                    if hasattr(concept, 'extracted_items') and concept.extracted_items:
                        for item in concept.extracted_items:
                            extracted_items.append(ExtractedItem(
                                content=str(item.value) if hasattr(item, 'value') else str(item),
                                confidence=getattr(item, 'confidence', None),
                                source_reference=getattr(item, 'reference', None),
                                metadata={
                                    "type": "concept_from_aspect_extraction",
                                    "aspect_name": aspect.name,
                                    "concept_name": concept.name,
                                    "concept_description": concept.description,
                                    "extraction_method": "contextgem_concept_from_aspect"
                                }
                            ))
        
        return extracted_items
    
    def _generate_summary(self, extracted_items: List[ExtractedItem], query: str) -> str:
        """
        生成查询结果摘要
        
        Args:
            extracted_items: 提取的信息项
            query: 原始查询
            
        Returns:
            str: 摘要文本
        """
        if not extracted_items:
            return f"未找到与查询 '{query}' 相关的信息。"
        
        item_count = len(extracted_items)
        extraction_methods = set(item.metadata.get("extraction_method", "unknown") for item in extracted_items)
        
        summary = f"根据查询 '{query}' 找到 {item_count} 个相关信息项。"
        summary += f"使用了 {', '.join(extraction_methods)} 提取方法。"
        
        # 添加主要内容预览
        if extracted_items:
            preview = extracted_items[0].content[:50000]
            if len(extracted_items[0].content) > 50000:
                preview += "..."
            summary += f" 主要内容包括：{preview}"
        
        return summary

    async def process_raw_text_extraction(
        self,
        text: str,
        query: str,
        language: QueryLanguage = QueryLanguage.AUTO
    ) -> ExtractionResult:
        """
        处理 RAW 模式查询 - 使用原生 LLM 直接回答用户查询

        Args:
            text: 要查询的文本内容
            query: 自然语言查询
            language: 查询语言

        Returns:
            ExtractionResult: 查询结果，包含 LLM 对查询的直接回答
        """
        start_time = time.time()

        try:
            app_logger.info(f"开始使用原生 LLM 直接回答查询: {query[:50]}...")

            # 使用原生 LLM 直接回答查询
            extracted_items = await self._extract_raw_text_with_llm(text, query, language)

            # 生成摘要
            if extracted_items:
                summary = f"LLM 直接回答完成。针对查询 '{query}' 提供了详细回答。"
            else:
                summary = f"LLM 无法基于给定文档内容回答查询 '{query}'。"

            processing_time = time.time() - start_time

            app_logger.info(f"LLM 直接查询回答完成，耗时: {processing_time:.2f}秒")

            return ExtractionResult(
                query=query,
                extracted_items=extracted_items,
                summary=summary,
                processing_time=processing_time,
                token_usage={
                    "prompt_tokens": 0,  # 这里可以从 LLM 响应中获取实际的 token 使用量
                    "completion_tokens": 0,
                    "total_tokens": 0
                }
            )

        except Exception as e:
            app_logger.error(f"LLM 直接查询回答失败: {str(e)}")
            raise QueryProcessingError(f"LLM 直接查询回答失败: {str(e)}")

    async def _extract_raw_text_with_llm(
        self,
        text: str,
        query: str,
        language: QueryLanguage = QueryLanguage.AUTO
    ) -> List[ExtractedItem]:
        """
        使用原生 LLM 直接回答用户查询

        Args:
            text: 源文本
            query: 查询内容
            language: 查询语言

        Returns:
            List[ExtractedItem]: LLM 对查询的直接回答
        """
        try:
            # 构建 LLM 提示词
            prompt = self._build_raw_extraction_prompt(text, query, language)

            # 调用 LLM
            response = await self._call_llm_for_raw_extraction(prompt)

            # 解析 LLM 响应
            extracted_items = self._parse_llm_raw_response(response, query)

            return extracted_items

        except Exception as e:
            app_logger.error(f"LLM 原始文本提取过程失败: {str(e)}")
            # 如果 LLM 调用失败，回退到关键词匹配方法
            app_logger.warning("LLM 提取失败，回退到关键词匹配方法")
            return self._extract_raw_text_content_fallback(text, query)

    def _build_raw_extraction_prompt(
        self,
        text: str,
        query: str,
        language: QueryLanguage
    ) -> str:
        """
        构建用于直接查询回答的 LLM 提示词

        Args:
            text: 源文本
            query: 查询内容
            language: 查询语言

        Returns:
            str: 构建的提示词
        """
        # 根据语言选择提示词模板
        if language == QueryLanguage.CHINESE or (language == QueryLanguage.AUTO and self._is_chinese_text(text)):
            prompt_template = """
你是一个专业的文档分析助手。请根据用户的需求，基于给定的文档内容回答。

用户需求：{query}

文档内容：
{text}

请根据用户需求给出回答：
"""
        else:
            prompt_template = """
You are a professional document analysis assistant. Please answer the user's requirement directly based on the given document content.

User Requirement: {query}

Document Content:
{text}

Please provide your answer based on the user requirement:
"""

        return prompt_template.format(query=query, text=text)  # 限制文本长度避免超过上下文窗口

    async def _call_llm_for_raw_extraction(self, prompt: str) -> str:
        """
        调用 LLM 进行原始文本提取

        Args:
            prompt: 构建的提示词

        Returns:
            str: LLM 的响应
        """
        try:
            # 使用 litellm 调用配置的 LLM
            import litellm

            # 设置 LLM 配置
            model = settings.openai_model
            if not model.startswith("openai/"):
                model = f"openai/{model}"

            # 构建消息
            messages = [
                {
                    "role": "system",
                    "content": "你是一个专业的文档分析助手，专门负责基于文档内容直接回答用户的问题。"
                },
                {
                    "role": "user",
                    "content": prompt
                }
            ]

            # 调用 LLM
            response = await litellm.acompletion(
                model=model,
                messages=messages,
                api_key=settings.openai_api_key,
                api_base=settings.openai_base_url,
                temperature=0.1,  # 低温度确保一致性
                timeout=600
            )

            return response.choices[0].message.content

        except Exception as e:
            app_logger.error(f"LLM 调用失败: {str(e)}")
            raise

    def _parse_llm_raw_response(self, response: str, query: str) -> List[ExtractedItem]:
        """
        解析 LLM 的直接查询回答响应

        Args:
            response: LLM 的响应
            query: 原始查询

        Returns:
            List[ExtractedItem]: 解析后的提取项
        """
        extracted_items = []

        if not response or not response.strip():
            return extracted_items

        # 对于直接回答模式，将整个响应作为一个完整的回答
        response = response.strip()

        # 如果响应很长，可以按段落分割以便更好地展示
        if len(response) > 100000:
            # 按段落分割长回答
            paragraphs = [p.strip() for p in response.split('\n\n') if p.strip()]

            for i, paragraph in enumerate(paragraphs):
                extracted_items.append(ExtractedItem(
                    content=paragraph,
                    confidence=0.95,  # 高置信度，因为是 LLM 直接回答
                    source_reference=f"LLM回答段落 {i + 1}",
                    metadata={
                        "type": "llm_direct_answer",
                        "paragraph_index": i + 1,
                        "extraction_method": "llm_query_answer",
                        "query": query,
                        "model_used": settings.openai_model,
                        "is_complete_answer": len(paragraphs) == 1
                    }
                ))
        else:
            # 短回答作为单个项目
            extracted_items.append(ExtractedItem(
                content=response,
                confidence=0.95,
                source_reference="LLM Answer",
                metadata={
                    "type": "llm_direct_answer",
                    "extraction_method": "llm_query_answer",
                    "query": query,
                    "model_used": settings.openai_model,
                    "is_complete_answer": True
                }
            ))

        return extracted_items

    def _is_chinese_text(self, text: str) -> bool:
        """
        检测文本是否主要为中文

        Args:
            text: 要检测的文本

        Returns:
            bool: 是否为中文文本
        """
        if not text:
            return False

        chinese_chars = sum(1 for char in text if '\u4e00' <= char <= '\u9fff')
        total_chars = len([c for c in text if c.isalnum()])

        if total_chars == 0:
            return False

        return chinese_chars / total_chars > 0.3

    def _extract_raw_text_content_fallback(self, text: str, query: str) -> List[ExtractedItem]:
        """
        从文本中提取与查询相关的原始内容 - 关键词匹配回退方法

        Args:
            text: 源文本
            query: 查询内容

        Returns:
            List[ExtractedItem]: 提取的原始文本项
        """
        extracted_items = []

        # 分割文本为段落
        paragraphs = text.split('\n\n')

        # 提取查询中的关键词
        query_keywords = self._extract_keywords_from_query(query)

        for i, paragraph in enumerate(paragraphs):
            paragraph = paragraph.strip()
            if not paragraph:
                continue

            # 检查段落是否包含查询关键词
            relevance_score = self._calculate_relevance_score(paragraph, query_keywords)

            if relevance_score > 0:
                extracted_items.append(ExtractedItem(
                    content=paragraph,
                    confidence=min(relevance_score / len(query_keywords), 1.0),
                    source_reference=f"段落 {i + 1}",
                    metadata={
                        "type": "raw_text_extraction_fallback",
                        "paragraph_index": i + 1,
                        "relevance_score": relevance_score,
                        "extraction_method": "keyword_matching_fallback",
                        "keywords_matched": [kw for kw in query_keywords if kw.lower() in paragraph.lower()]
                    }
                ))

        # 按相关性得分排序
        extracted_items.sort(key=lambda x: x.confidence, reverse=True)

        return extracted_items

    def _extract_keywords_from_query(self, query: str) -> List[str]:
        """
        从查询中提取关键词

        Args:
            query: 查询文本

        Returns:
            List[str]: 关键词列表
        """
        # 移除常见的停用词
        stop_words = {
            '的', '了', '在', '是', '我', '有', '和', '就', '不', '人', '都', '一', '一个', '上', '也', '很', '到', '说', '要', '去', '你', '会', '着', '没有', '看', '好', '自己', '这',
            'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by', 'is', 'are', 'was', 'were', 'be', 'been', 'have', 'has', 'had', 'do', 'does', 'did',
            '什么', '哪些', '如何', '怎么', '为什么', '什么时候', '哪里', '谁', '多少', '有哪些', '包括', '包含', '涉及', '关于', '相关', '主要', '重要', '详细', '具体'
        }

        keywords = []

        # 1. 提取完整的专业术语（中英文）
        import re

        # 匹配中文专业术语（2-6个字符的中文词组）
        chinese_terms = re.findall(r'[\u4e00-\u9fff]{2,6}', query)
        for term in chinese_terms:
            if term not in stop_words and len(term) >= 2:
                keywords.append(term)

        # 匹配英文单词
        english_words = re.findall(r'\b[a-zA-Z]{2,}\b', query.lower())
        for word in english_words:
            if word not in stop_words and len(word) >= 2:
                keywords.append(word)

        # 2. 如果没有找到关键词，使用简单分词
        if not keywords:
            words = re.findall(r'\b\w+\b', query.lower())
            keywords = [word for word in words if len(word) > 1 and word not in stop_words]

        # 3. 去重并保持顺序
        seen = set()
        unique_keywords = []
        for keyword in keywords:
            if keyword not in seen:
                seen.add(keyword)
                unique_keywords.append(keyword)

        return unique_keywords

    def _calculate_relevance_score(self, text: str, keywords: List[str]) -> float:
        """
        计算文本与关键词的相关性得分

        Args:
            text: 文本内容
            keywords: 关键词列表

        Returns:
            float: 相关性得分
        """
        if not keywords:
            return 0.0

        text_lower = text.lower()
        score = 0.0
        matched_keywords = []

        for keyword in keywords:
            keyword_lower = keyword.lower()

            # 精确匹配
            exact_count = text_lower.count(keyword_lower)
            if exact_count > 0:
                score += 1.0 + (exact_count - 1) * 0.1
                matched_keywords.append(keyword)
                continue

            # 部分匹配（对于较长的关键词）
            if len(keyword) >= 3:
                # 检查是否包含关键词的子串
                if keyword_lower in text_lower:
                    score += 0.5
                    matched_keywords.append(keyword)
                    continue

                # 检查文本是否包含关键词的部分
                for i in range(len(keyword) - 2):
                    substr = keyword_lower[i:i+3]
                    if substr in text_lower:
                        score += 0.2
                        break

        # 如果没有任何匹配，但文本包含相关概念，给予小分
        if score == 0.0:
            # 检查一些通用的相关词汇
            related_terms = {
                '机器学习': ['学习', '算法', '模型', '训练', '数据'],
                '深度学习': ['神经', '网络', '深度', '学习'],
                '人工智能': ['智能', 'ai', '人工'],
                '神经网络': ['神经', '网络', '节点', '层'],
                '医疗': ['医学', '健康', '诊断', '治疗', '病'],
                '应用': ['使用', '应用', '实现', '技术']
            }

            for main_term, related in related_terms.items():
                if any(kw in main_term for kw in keywords):
                    for term in related:
                        if term in text_lower:
                            score += 0.1
                            break

        return score

    def _split_document_text(self, text: str, max_chunk_size: int = 8000) -> List[str]:
        """
        将长文档分割成较小的块

        Args:
            text: 文档文本
            max_chunk_size: 每块的最大字符数

        Returns:
            List[str]: 文本块列表
        """
        if len(text) <= max_chunk_size:
            return [text]

        chunks = []
        # 按段落分割
        paragraphs = text.split('\n\n')
        current_chunk = ""

        for paragraph in paragraphs:
            # 如果单个段落就超过限制，需要进一步分割
            if len(paragraph) > max_chunk_size:
                if current_chunk:
                    chunks.append(current_chunk.strip())
                    current_chunk = ""

                # 按句子分割长段落
                sentences = paragraph.split('。')
                for sentence in sentences:
                    if len(current_chunk + sentence + '。') > max_chunk_size:
                        if current_chunk:
                            chunks.append(current_chunk.strip())
                        current_chunk = sentence + '。'
                    else:
                        current_chunk += sentence + '。'
            else:
                # 检查添加这个段落是否会超过限制
                if len(current_chunk + '\n\n' + paragraph) > max_chunk_size:
                    if current_chunk:
                        chunks.append(current_chunk.strip())
                    current_chunk = paragraph
                else:
                    if current_chunk:
                        current_chunk += '\n\n' + paragraph
                    else:
                        current_chunk = paragraph

        # 添加最后一块
        if current_chunk:
            chunks.append(current_chunk.strip())

        return chunks

    async def process_large_document_query(
        self,
        contextgem_doc: Document,
        query: str,
        language: QueryLanguage = QueryLanguage.AUTO,
        extraction_mode: ExtractionMode = ExtractionMode.SIMPLE,
        max_chunk_size: int = 8000,
        max_paragraphs_per_call: int = 30,
        max_items_per_call: int = 1,
        use_concurrency: bool = True
    ) -> ExtractionResult:
        """
        处理大文档查询 - 使用 ContextGem 官方优化参数避免上下文窗口超限

        Args:
            contextgem_doc: ContextGem 文档对象
            query: 自然语言查询
            language: 查询语言
            extraction_mode: 提取模式
            max_chunk_size: 每块的最大字符数（用于预检查）
            max_paragraphs_per_call: 每次 LLM 调用分析的最大段落数
            max_items_per_call: 每次调用处理的最大概念/方面数量
            use_concurrency: 是否启用并发处理

        Returns:
            ExtractionResult: 提取结果
        """
        start_time = time.time()

        try:
            app_logger.info(f"开始处理大文档查询: {query[:50]}...")

            # 检查是否为 RAW 模式
            if extraction_mode == ExtractionMode.RAW:
                app_logger.info("大文档使用 RAW 模式进行原始文本提取")
                return await self.process_raw_text_extraction(contextgem_doc.raw_text, query, language)

            # 检查文档大小
            doc_text = contextgem_doc.raw_text
            app_logger.info(f"处理大文档 ({len(doc_text)} 字符)，使用 ContextGem 官方优化参数")

            # 1. 定义方面（Aspect）
            aspect = self._create_aspect_for_query(query, extraction_mode)

            # 2. 添加方面到文档
            contextgem_doc.add_aspects([aspect])

            # 3. 使用 ContextGem 官方推荐的长文档处理参数
            app_logger.info(f"使用 ContextGem 优化参数: max_paragraphs_per_call={max_paragraphs_per_call}, max_items_per_call={max_items_per_call}, use_concurrency={use_concurrency}")

            extracted_aspects = self.llm.extract_aspects_from_document(
                contextgem_doc,
                max_paragraphs_to_analyze_per_call=max_paragraphs_per_call,
                max_items_per_call=max_items_per_call,
                use_concurrency=use_concurrency
            )

            # 4. 转换结果格式
            extracted_items = self._convert_aspects_to_items(extracted_aspects, query)

            # 生成摘要
            summary = self._generate_summary(extracted_items, query)

            processing_time = time.time() - start_time

            app_logger.info(f"大文档查询处理完成，耗时: {processing_time:.2f}秒")

            return ExtractionResult(
                query=query,
                extracted_items=extracted_items,
                summary=summary,
                processing_time=processing_time,
                token_usage={
                    "prompt_tokens": 0,
                    "completion_tokens": 0,
                    "total_tokens": 0
                }
            )

        except Exception as e:
            app_logger.error(f"大文档查询处理失败: {str(e)}")
            raise QueryProcessingError(f"大文档查询处理失败: {str(e)}")

    async def process_batch_queries(
        self,
        text: str,
        queries: List[str],
        language: QueryLanguage = QueryLanguage.AUTO,
        extraction_mode: ExtractionMode = ExtractionMode.SIMPLE
    ) -> List[ExtractionResult]:
        """
        批量处理查询

        Args:
            text: 要查询的文本内容
            queries: 查询列表
            language: 查询语言
            extraction_mode: 提取模式

        Returns:
            List[ExtractionResult]: 提取结果列表
        """
        results = []

        for query in queries:
            try:
                # 对于 RAW 模式，使用专门的原始文本提取
                if extraction_mode == ExtractionMode.RAW:
                    result = await self.process_raw_text_extraction(text, query, language)
                else:
                    result = await self.process_text_query(
                        text=text,
                        query=query,
                        language=language,
                        extraction_mode=extraction_mode
                    )
                results.append(result)
            except Exception as e:
                app_logger.error(f"批量查询中的单个查询失败: {query}, 错误: {str(e)}")
                # 创建失败结果
                results.append(ExtractionResult(
                    query=query,
                    extracted_items=[],
                    summary=f"查询失败: {str(e)}",
                    processing_time=0.0,
                    token_usage={
                        "prompt_tokens": 0,
                        "completion_tokens": 0,
                        "total_tokens": 0
                    }
                ))

        return results

    def get_cost_info(self) -> Dict[str, Any]:
        """
        获取成本信息

        Returns:
            Dict[str, Any]: 成本信息
        """
        return {
            "total_cost": 0.0,
            "input_cost": 0.0,
            "output_cost": 0.0,
            "currency": "USD"
        }
