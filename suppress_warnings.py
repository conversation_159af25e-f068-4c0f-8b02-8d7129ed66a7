#!/usr/bin/env python3
"""
抑制Pydantic序列化警告的工具脚本
"""

import warnings
import os

def suppress_pydantic_warnings():
    """抑制Pydantic相关的序列化警告"""
    
    # 抑制Pydantic序列化警告
    warnings.filterwarnings(
        "ignore", 
        category=UserWarning, 
        module="pydantic",
        message=".*PydanticSerializationUnexpectedValue.*"
    )
    
    # 抑制LiteLLM相关的警告
    warnings.filterwarnings(
        "ignore",
        category=UserWarning,
        module="litellm"
    )
    
    print("已抑制Pydantic和LiteLLM相关警告")

if __name__ == "__main__":
    suppress_pydantic_warnings()
