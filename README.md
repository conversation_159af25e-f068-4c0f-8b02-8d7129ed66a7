# ExtracInfo - 智能文档信息提取服务

[![Python](https://img.shields.io/badge/Python-3.11+-blue.svg)](https://python.org)
[![FastAPI](https://img.shields.io/badge/FastAPI-0.104+-green.svg)](https://fastapi.tiangolo.com)
[![ContextGem](https://img.shields.io/badge/ContextGem-Latest-orange.svg)](https://contextgem.dev)
[![License](https://img.shields.io/badge/License-MIT-yellow.svg)](LICENSE)

ExtracInfo 是一个基于 **ContextGem** 和 **FastAPI** 构建的智能文档信息提取服务，支持多种文档格式的自然语言查询和结构化信息提取。

## ✨ 核心特性

### 🔍 **智能查询处理**
- **自然语言查询**: 支持中英文自然语言查询
- **四种提取模式**: Simple、Detailed、Structured、RAW 四种提取模式
- **批量查询**: 支持一次处理多个查询请求
- **实时处理**: 基于 ContextGem 的高效文档分析
- **智能问答**: RAW 模式支持直接 LLM 问答，无需预定义概念

### 📄 **多格式文档支持**
- **DOCX**: 使用官方 ContextGem DocxConverter，支持表格、图片、样式
- **Excel**: 支持 .xlsx/.xls 格式，智能提取数据和统计信息
- **CSV**: 支持逗号分隔值文件，自动编码检测和数据分析
- **文本文件**: 支持 .txt 和 .md 格式
- **智能解析**: 保持文档结构和格式信息，专门优化表格数据处理

### 🤖 **先进的 AI 技术**
- **ContextGem 集成**: 基于官方文档的完整集成
- **SaT 模型**: 智能句子分割和文档分析
- **多 LLM 支持**: 支持 OpenAI、DeepSeek、Moonshot 等多种模型
- **原生 LLM 查询**: RAW 模式直接调用 LLM 进行智能问答
- **自适应语言**: 根据内容自动适配输出语言
- **表格数据智能分析**: 专门优化的表格数据查询和统计

### 🚀 **企业级特性**
- **RESTful API**: 完整的 REST API 接口
- **异步处理**: 高并发异步请求处理
- **错误恢复**: 完善的异常处理和降级机制
- **日志监控**: 详细的操作日志和性能监控

## 🏗️ 技术架构

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   FastAPI       │    │   ContextGem    │    │   Document      │
│   Web Server    │───▶│   Query Engine  │───▶│   Processors    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   API Routes    │    │   Aspect/       │    │   DocxConverter │
│   & Validation  │    │   Concept       │    │   TabularConv   │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   LLM Direct    │    │   RAW Mode      │    │   CSV/Excel     │
│   Query (RAW)   │    │   Processing    │    │   Specialized   │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### 核心组件

- **FastAPI**: 高性能 Web 框架，提供 API 接口
- **ContextGem**: 官方文档分析引擎，支持智能信息提取
- **LiteLLM**: 统一的 LLM 接口，支持多种模型提供商
- **TabularConverter**: 专门的表格数据转换器，支持 CSV/Excel
- **Pydantic**: 数据验证和序列化
- **Uvicorn**: ASGI 服务器，支持高并发

## 🚀 快速开始

### 环境要求

- Python 3.11+
- 8GB+ RAM (推荐)
- 支持的操作系统: Windows, macOS, Linux

### 安装步骤

1. **克隆项目**
```bash
git clone https://github.com/your-repo/ExtracInfo.git
cd ExtracInfo
```

2. **安装依赖**
```bash
# 使用 uv (推荐)
uv sync

# 或使用 pip
pip install -e .

# 手动安装关键依赖
pip install fastapi uvicorn contextgem litellm pandas openpyxl
```

3. **配置环境变量**
```bash
# 复制环境变量模板
cp .env.example .env

# 编辑配置文件
nano .env
```

4. **启动服务**
```bash
# 开发模式
uv run python -m app.main

# 生产模式
uvicorn app.main:app --host 0.0.0.0 --port 8000
```

### 环境配置

在 `.env` 文件中配置以下参数：

```env
# LLM API 配置 (用于 RAW 模式)
OPENAI_API_KEY=your_api_key_here
OPENAI_MODEL=gpt-3.5-turbo
OPENAI_BASE_URL=https://api.openai.com/v1

# 应用配置
APP_NAME=ExtracInfo
APP_VERSION=0.1.0
DEBUG=true

# 文件上传配置
UPLOAD_DIR=uploads
MAX_FILE_SIZE=10485760

# RAW 模式配置
RAW_MODE_ENABLED=true
RAW_MODE_TIMEOUT=600
RAW_MODE_MAX_TOKENS=4000
```

## 📖 API 使用指南

### 基础 API

#### 健康检查
```bash
GET /health
```

#### 文档上传
```bash
POST /api/v1/upload
Content-Type: multipart/form-data

# 表单数据
file: [文档文件]
```

### 查询 API

#### 文本查询
```bash
POST /api/v1/query/text
Content-Type: application/json

{
  "text": "人工智能技术包括机器学习、深度学习等...",
  "query": "AI技术有哪些分类？",
  "language": "zh",
  "extraction_mode": "detailed"
}
```

#### 文档查询
```bash
POST /api/v1/query/document
Content-Type: application/json

{
  "document_id": "doc-uuid-here",
  "query": "文档中提到了哪些技术？",
  "language": "zh",
  "extraction_mode": "simple"
}
```

#### 批量查询
```bash
POST /api/v1/query/batch
Content-Type: application/json

{
  "queries": [
    "什么是机器学习？",
    "深度学习的应用有哪些？"
  ],
  "text": "文档内容...",
  "language": "zh",
  "extraction_mode": "structured"
}
```

#### 上传并查询 (新功能)
```bash
POST /api/v1/query/upload-and-query
Content-Type: multipart/form-data

# 表单数据
file: [文档文件]
query: "要查询的问题"
language: "zh"
extraction_mode: "detailed"
```

**特点**：
- 一次性完成文档上传和查询
- 支持临时文档分析，无需保存
- 适合批量文档处理脚本
- 支持 DOCX、Excel、CSV、TXT、MD 格式
- 表格数据自动分析和统计

### 提取模式说明

| 模式 | 描述 | 适用场景 | 技术实现 |
|------|------|----------|----------|
| `simple` | 基础信息提取 | 快速获取关键信息 | ContextGem StringConcept |
| `detailed` | 详细信息提取 | 需要完整上下文的场景 | 多概念组合提取 |
| `structured` | 结构化提取 | 需要格式化输出的场景 | JsonObjectConcept |
| `raw` | 智能问答模式 | 直接回答用户问题 | **原生 LLM 直接调用** |

#### RAW 模式特色功能
- **直接问答**: 不再局限于文本提取，而是基于文档内容直接回答用户问题
- **智能理解**: 使用原生 LLM 理解查询意图，提供准确回答
- **上下文保持**: 保持文档上下文，确保回答的准确性和相关性
- **多语言支持**: 自动检测语言，提供对应语言的回答
- **降级机制**: LLM 调用失败时自动回退到关键词匹配

## 💡 使用示例

### 表格数据查询示例

假设有一个员工信息 CSV 文件：
```csv
姓名,年龄,部门,工资,入职日期
张三,28,销售部,8000,2022-01-15
李四,32,技术部,12000,2021-03-20
王五,25,市场部,7500,2022-06-10
```

#### 查询示例 1: 部门统计
```bash
POST /api/v1/query/upload-and-query
# 查询: "各部门的平均工资是多少？"
# RAW 模式回答:
"根据数据分析：
- 技术部平均工资: 12000元 (1人)
- 销售部平均工资: 8000元 (1人)
- 市场部平均工资: 7500元 (1人)
技术部工资最高，市场部工资最低。"
```

#### 查询示例 2: 条件筛选
```bash
# 查询: "年龄大于30的员工有哪些？"
# RAW 模式回答:
"年龄大于30的员工有1人：
李四，32岁，技术部，工资12000元，2021年3月入职。"
```

#### 查询示例 3: 趋势分析
```bash
# 查询: "2022年入职的员工情况如何？"
# RAW 模式回答:
"2022年共有2名员工入职：
1. 张三 - 销售部，28岁，工资8000元
2. 王五 - 市场部，25岁，工资7500元
平均年龄26.5岁，平均工资7750元。"
```

### 文档查询示例

#### DOCX 技术文档查询
```bash
# 查询: "这个系统的主要技术架构是什么？"
# Detailed 模式提取:
{
  "主要信息": "系统采用微服务架构...",
  "详细描述": "包含API网关、服务注册中心...",
  "信息完整性": true
}
```

#### RAW 模式智能问答
```bash
# 查询: "如何部署这个系统？"
# RAW 模式回答:
"根据文档，系统部署步骤如下：
1. 环境准备：安装 Docker 和 Kubernetes
2. 配置文件：修改 config.yaml 中的数据库连接
3. 镜像构建：运行 docker build 命令
4. 服务部署：使用 kubectl apply 部署到集群
5. 健康检查：访问 /health 端点验证服务状态"
```

## 📊 表格数据处理

### CSV 文件支持

ExtracInfo 提供了强大的 CSV 文件处理能力：

#### 自动编码检测
```python
# 支持多种编码格式
encodings = ['utf-8', 'gbk', 'gb2312', 'latin1', 'cp1252']
```

#### 智能数据分析
- **列类型识别**: 自动识别数值列、文本列、日期列
- **统计信息**: 自动计算平均值、最大值、最小值、标准差
- **数据质量**: 检测空值、唯一值数量
- **数据预览**: 智能显示前N行数据

#### 查询示例
```bash
# CSV 数据查询示例
{
  "query": "技术部有哪些员工？",
  "extraction_mode": "raw"
}

# 返回结果
{
  "extracted_items": [
    {
      "content": "根据数据分析，技术部共有3名员工：\n1. 李四，32岁，工资12000元\n2. 赵六，35岁，工资15000元\n3. 吴十，33岁，工资13500元\n\n技术部是工资最高的部门，平均工资为13500元。",
      "confidence": 0.95,
      "metadata": {
        "extraction_method": "llm_query_answer"
      }
    }
  ]
}
```

### Excel 文件支持

#### 多工作表处理
- **自动识别**: 读取所有工作表
- **结构分析**: 分析每个工作表的结构
- **数据整合**: 跨工作表数据关联分析

#### 高级功能
- **公式识别**: 保留计算结果
- **格式保持**: 保持数据格式信息
- **图表描述**: 提取图表相关信息（如果有）

### 表格查询优化

#### 智能关键词检测
系统会自动检测表格相关查询：
```python
tabular_keywords = [
    '列', '行', '表格', '数据', '统计', '平均', '最大', '最小',
    '总和', '计算', '字段', '记录', '条目', '汇总', '分组'
]
```

#### 专门的概念设计
针对表格数据设计了专门的提取概念：
- **表格结构信息**: 列名、数据类型、基本结构
- **数据内容**: 具体数据行和值
- **统计分析**: 统计信息和计算结果
- **数据完整性**: 数据质量评估

## 🔧 开发指南

### 项目结构

```
ExtracInfo/
├── app/
│   ├── api/                    # API 路由和模型
│   │   ├── models/            # 请求/响应模型
│   │   └── routes/            # API 路由
│   ├── core/                  # 核心配置
│   ├── services/              # 业务逻辑服务
│   │   ├── contextgem_converter.py      # ContextGem 转换器
│   │   ├── contextgem_query_processor.py # 查询处理器 (含 RAW 模式)
│   │   ├── document_processor.py        # 文档处理器
│   │   └── tabular_converter.py         # 表格数据转换器 (新增)
│   └── utils/                 # 工具函数
├── uploads/                   # 文件上传目录
├── tests/                     # 测试文件
├── requirements.txt           # 依赖列表
└── README.md                  # 项目文档
```

### 添加新的文档格式支持

1. **扩展 DocumentFormat 枚举**
```python
# app/api/models/requests.py
class DocumentFormat(str, Enum):
    PDF = "pdf"  # 新增格式
```

2. **实现格式处理器**
```python
# app/services/document_processor.py
async def extract_text_from_pdf(self, file_path: Path) -> str:
    # 实现 PDF 文本提取逻辑
    pass
```

3. **更新转换器**
```python
# app/services/contextgem_converter.py
def is_supported_format(self, file_path: Path) -> bool:
    return file_path.suffix.lower() in ['.docx', '.pdf']
```

### 自定义查询处理

```python
# 创建自定义概念
from contextgem import StringConcept, BooleanConcept

custom_concept = StringConcept(
    name="技术特点",
    description="提取技术的主要特点和优势",
    add_references=True,
    reference_depth="sentences"
)
```

## 🧪 测试

### 运行测试

```bash
# 运行所有测试
pytest

# 运行特定测试
pytest tests/test_api.py

# 生成覆盖率报告
pytest --cov=app tests/
```

### API 测试

```bash
# 测试文档上传和查询
python test_api_upload.py

# 测试 ContextGem 集成
python test_contextgem.py
```

## 🔧 配置选项

### 模型配置

支持多种 LLM 提供商：

```env
# OpenAI
OPENAI_MODEL=gpt-3.5-turbo
OPENAI_BASE_URL=https://api.openai.com/v1

# DeepSeek
OPENAI_MODEL=DeepSeek-V3
OPENAI_BASE_URL=https://api.deepseek.com/v1

# Moonshot AI
OPENAI_MODEL=moonshot-v1-8k
OPENAI_BASE_URL=https://api.moonshot.cn/v1
```

### 性能调优

```env
# 文件上传限制
MAX_FILE_SIZE=52428800  # 50MB

# 并发限制
MAX_CONCURRENT_REQUESTS=10

# 缓存配置
ENABLE_CACHE=true
CACHE_TTL=3600
```

## 📊 性能指标

### 基准测试结果

| 文档类型 | 文件大小 | 处理时间 | 提取准确率 | 特殊功能 |
|----------|----------|----------|------------|----------|
| DOCX | 1MB | ~15s | 95%+ | ContextGem 官方转换器 |
| Excel | 2MB | ~8s | 90%+ | 多工作表支持 |
| CSV | 1MB | ~5s | 95%+ | 自动编码检测 |
| 文本 | 500KB | ~3s | 98%+ | 基础文本处理 |
| RAW 模式 | 任意 | ~10-30s | 98%+ | LLM 直接问答 |

### 系统要求

- **最小配置**: 4GB RAM, 2 CPU 核心
- **推荐配置**: 8GB RAM, 4 CPU 核心
- **高负载配置**: 16GB RAM, 8 CPU 核心

## 🤝 贡献指南

我们欢迎社区贡献！请遵循以下步骤：

1. Fork 项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 开启 Pull Request

### 代码规范

- 使用 Black 进行代码格式化
- 遵循 PEP 8 编码规范
- 添加适当的类型注解
- 编写单元测试

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 🙏 致谢

- [ContextGem](https://contextgem.dev) - 强大的文档分析引擎
- [FastAPI](https://fastapi.tiangolo.com) - 现代化的 Web 框架
- [Pydantic](https://pydantic-docs.helpmanual.io) - 数据验证库

## 📞 支持与联系

- 📧 邮箱: <EMAIL>
- 🐛 问题反馈: [GitHub Issues](https://github.com/your-repo/ExtracInfo/issues)
- 📖 文档: [在线文档](https://docs.extractinfo.com)
- 💬 社区讨论: [GitHub Discussions](https://github.com/your-repo/ExtracInfo/discussions)

---

**ExtracInfo** - 让文档信息提取变得简单高效 🚀