"""
查询处理服务模块

负责处理自然语言查询，集成 ContextGem 进行信息提取
"""

import time
import asyncio
from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime

# 集成 ContextGem
from contextgem import DocumentLLM, Document, DocumentPipeline, Aspect, StringConcept, BooleanConcept
from aiolimiter import AsyncLimiter

from app.core.config import settings
from app.core.exceptions import QueryProcessingError, ContextGemError, ConfigurationError
from app.api.models.requests import QueryLanguage, ExtractionMode, PipelineConfigRequest
from app.api.models.responses import ExtractionResult, ExtractedItem
from app.utils.logger import app_logger


class QueryProcessor:
    """查询处理器类"""
    
    def __init__(self):
        """初始化查询处理器"""
        self.llm = None
        self.cost_tracker = {}
        self._initialize_llm()
    
    def _initialize_llm(self) -> None:
        """初始化 ContextGem LLM"""
        try:
            # 检查必要的配置
            if not settings.openai_api_key:
                raise ConfigurationError("未配置 OpenAI API 密钥")

            # 对于 OpenAI 兼容端点，设置环境变量
            import os
            if settings.openai_model.startswith("openai/"):
                # 使用 OpenAI 兼容端点，设置标准环境变量
                os.environ["OPENAI_API_KEY"] = settings.openai_api_key
                if settings.openai_base_url:
                    os.environ["OPENAI_API_BASE"] = settings.openai_base_url

            # 创建 DocumentLLM 实例
            # 确保模型名称格式正确
            model_name = settings.openai_model
            if not model_name.startswith("openai/"):
                model_name = f"openai/{model_name}"

            llm_config = {
                "model": model_name,
                "api_key": settings.openai_api_key,
                "role": "extractor_text",
                "temperature": 0.3,
                "max_tokens": 4096,
                "output_language": "adapt",  # 自适应语言
                "async_limiter": AsyncLimiter(3000, 10)  # 每10秒最多3个请求
            }

            # 如果配置了自定义 api_base，添加到配置中
            if settings.openai_base_url:
                llm_config["api_base"] = settings.openai_base_url

            # 对于 OpenAI 兼容端点，不需要额外的默认配置

            self.llm = DocumentLLM(**llm_config)

            app_logger.info(f"ContextGem LLM 初始化成功: {settings.default_model}")

        except Exception as e:
            app_logger.error(f"ContextGem LLM 初始化失败: {str(e)}")
            raise ContextGemError(f"LLM 初始化失败: {str(e)}")
    
    def _create_dynamic_pipeline(self, query: str, extraction_mode: ExtractionMode) -> DocumentPipeline:
        """
        根据查询动态创建处理管道

        Args:
            query: 用户查询
            extraction_mode: 提取模式

        Returns:
            DocumentPipeline: 动态创建的管道
        """
        try:
            # 根据提取模式确定概念类型和数量
            if extraction_mode == ExtractionMode.SIMPLE:
                concepts = [
                    StringConcept(
                        name="查询结果",
                        description=f"根据查询'{query}'提取的相关信息",
                        add_references=True,
                        reference_depth="sentences"
                    )
                ]
            elif extraction_mode == ExtractionMode.DETAILED:
                concepts = [
                    StringConcept(
                        name="主要信息",
                        description=f"根据查询'{query}'提取的主要信息",
                        add_references=True,
                        reference_depth="sentences"
                    ),
                    StringConcept(
                        name="详细描述",
                        description=f"与查询'{query}'相关的详细描述和说明",
                        add_references=True,
                        reference_depth="sentences"
                    ),
                    BooleanConcept(
                        name="信息完整性",
                        description="提取的信息是否完整和充分",
                        add_justifications=True
                    )
                ]
            else:  # STRUCTURED
                concepts = [
                    StringConcept(
                        name="关键信息",
                        description=f"根据查询'{query}'提取的关键信息点",
                        add_references=True,
                        reference_depth="sentences"
                    ),
                    StringConcept(
                        name="支持证据",
                        description="支持提取信息的证据和引用",
                        add_references=True,
                        reference_depth="sentences"
                    ),
                    StringConcept(
                        name="相关背景",
                        description="与查询相关的背景信息和上下文",
                        add_references=True,
                        reference_depth="sentences"
                    )
                ]

            # 创建主要方面
            main_aspect = Aspect(
                name="信息提取",
                description=f"基于查询'{query}'的信息提取",
                concepts=concepts
            )

            # 创建管道
            pipeline = DocumentPipeline(
                aspects=[main_aspect]
            )

            return pipeline

        except Exception as e:
            app_logger.error(f"创建动态管道失败: {str(e)}")
            raise QueryProcessingError(f"创建处理管道失败: {str(e)}")
    
    def _extract_results_from_document(self, doc: Document, query: str) -> List[ExtractedItem]:
        """
        从处理后的文档中提取结果

        Args:
            doc: 处理后的 ContextGem 文档
            query: 原始查询

        Returns:
            List[ExtractedItem]: 提取的信息项列表
        """
        extracted_items = []

        try:
            # 获取主要方面
            main_aspect = doc.get_aspect_by_name("信息提取")

            if main_aspect and main_aspect.extracted_items:
                for aspect_item in main_aspect.extracted_items:
                    extracted_items.append(ExtractedItem(
                        content=aspect_item.value,
                        confidence=getattr(aspect_item, 'confidence', None),
                        source_reference=getattr(aspect_item, 'reference', None),
                        metadata={
                            "type": "aspect_extraction",
                            "aspect_name": main_aspect.name
                        }
                    ))

            # 处理概念提取结果
            for concept in main_aspect.concepts if main_aspect else []:
                if concept.extracted_items:
                    for concept_item in concept.extracted_items:
                        # 处理不同类型的概念
                        if hasattr(concept_item, 'value'):
                            content = str(concept_item.value)
                        else:
                            content = str(concept_item)

                        metadata = {
                            "type": "concept_extraction",
                            "concept_name": concept.name,
                            "concept_description": concept.description
                        }

                        # 添加理由（如果是布尔概念）
                        if hasattr(concept_item, 'justification') and concept_item.justification:
                            metadata["justification"] = concept_item.justification

                        extracted_items.append(ExtractedItem(
                            content=content,
                            confidence=getattr(concept_item, 'confidence', None),
                            source_reference=getattr(concept_item, 'reference', None),
                            metadata=metadata
                        ))

            return extracted_items

        except Exception as e:
            app_logger.error(f"提取结果失败: {str(e)}")
            return []
    
    async def process_text_query(
        self,
        text: str,
        query: str,
        language: QueryLanguage = QueryLanguage.AUTO,
        extraction_mode: ExtractionMode = ExtractionMode.SIMPLE,
        config: Optional[PipelineConfigRequest] = None
    ) -> ExtractionResult:
        """
        处理文本查询

        Args:
            text: 要分析的文本内容
            query: 自然语言查询
            language: 查询语言
            extraction_mode: 提取模式
            config: 管道配置

        Returns:
            ExtractionResult: 提取结果
        """
        start_time = time.time()

        try:
            app_logger.info(f"开始处理文本查询: {query[:50]}...")

            # 更新 LLM 配置（如果提供）
            if config:
                await self._update_llm_config(config)

            # 创建 ContextGem 文档
            doc = Document(raw_text=text)

            # 创建动态管道
            pipeline = self._create_dynamic_pipeline(query, extraction_mode)

            # 分配管道到文档
            doc.assign_pipeline(pipeline)

            # 执行提取
            processed_doc = await self._extract_with_llm(doc, config)

            # 提取结果
            extracted_items = self._extract_results_from_document(processed_doc, query)

            # 生成摘要
            summary = self._generate_summary(extracted_items, query)

            processing_time = time.time() - start_time

            # 获取令牌使用情况
            token_usage = self._get_token_usage()

            app_logger.info(f"文本查询处理完成，耗时: {processing_time:.2f}秒")

            return ExtractionResult(
                query=query,
                extracted_items=extracted_items,
                summary=summary,
                processing_time=processing_time,
                token_usage=token_usage
            )

        except Exception as e:
            app_logger.error(f"文本查询处理失败: {str(e)}")
            raise QueryProcessingError(f"查询处理失败: {str(e)}")
    
    async def process_document_query(
        self,
        document_content: str,
        query: str,
        language: QueryLanguage = QueryLanguage.AUTO,
        extraction_mode: ExtractionMode = ExtractionMode.SIMPLE,
        config: Optional[PipelineConfigRequest] = None
    ) -> ExtractionResult:
        """
        处理基于文档的查询

        Args:
            document_content: 文档内容
            query: 自然语言查询
            language: 查询语言
            extraction_mode: 提取模式
            config: 管道配置

        Returns:
            ExtractionResult: 提取结果
        """
        # 文档查询与文本查询的处理逻辑相同
        return await self.process_text_query(
            document_content, query, language, extraction_mode, config
        )

    async def process_contextgem_document_query(
        self,
        contextgem_doc: Document,
        query: str,
        language: QueryLanguage = QueryLanguage.AUTO,
        extraction_mode: ExtractionMode = ExtractionMode.SIMPLE,
        config: Optional[PipelineConfigRequest] = None
    ) -> ExtractionResult:
        """
        处理基于 ContextGem 文档的查询

        Args:
            contextgem_doc: ContextGem 文档对象
            query: 自然语言查询
            language: 查询语言
            extraction_mode: 提取模式
            config: 管道配置

        Returns:
            ExtractionResult: 提取结果
        """
        start_time = time.time()

        try:
            app_logger.info(f"开始处理 ContextGem 文档查询: {query[:50]}...")

            # 更新 LLM 配置（如果提供）
            if config:
                await self._update_llm_config(config)

            # 创建动态管道
            pipeline = self._create_dynamic_pipeline(query, extraction_mode)

            # 分配管道到文档（覆盖现有的管道）
            contextgem_doc.assign_pipeline(pipeline, overwrite_existing=True)

            # 执行提取
            processed_doc = await self._extract_with_llm(contextgem_doc, config)

            # 提取结果
            extracted_items = self._extract_results_from_document(processed_doc, query)

            # 生成摘要
            summary = self._generate_summary(extracted_items, query)

            processing_time = time.time() - start_time

            # 获取令牌使用情况
            token_usage = self._get_token_usage()

            app_logger.info(f"ContextGem 文档查询处理完成，耗时: {processing_time:.2f}秒")

            return ExtractionResult(
                query=query,
                extracted_items=extracted_items,
                summary=summary,
                processing_time=processing_time,
                token_usage=token_usage
            )

        except Exception as e:
            app_logger.error(f"ContextGem 文档查询处理失败: {str(e)}")
            raise QueryProcessingError(f"查询处理失败: {str(e)}")
    
    async def process_batch_queries(
        self,
        text: str,
        queries: List[str],
        language: QueryLanguage = QueryLanguage.AUTO,
        extraction_mode: ExtractionMode = ExtractionMode.SIMPLE,
        config: Optional[PipelineConfigRequest] = None
    ) -> List[ExtractionResult]:
        """
        批量处理查询
        
        Args:
            text: 要分析的文本内容
            queries: 查询列表
            language: 查询语言
            extraction_mode: 提取模式
            config: 管道配置
            
        Returns:
            List[ExtractionResult]: 提取结果列表
        """
        results = []
        
        try:
            app_logger.info(f"开始批量处理 {len(queries)} 个查询")
            
            # 根据配置决定是否并发处理
            use_concurrency = config.use_concurrency if config else False
            
            if use_concurrency:
                # 并发处理
                tasks = [
                    self.process_text_query(text, query, language, extraction_mode, config)
                    for query in queries
                ]
                results = await asyncio.gather(*tasks, return_exceptions=True)
                
                # 处理异常结果
                processed_results = []
                for i, result in enumerate(results):
                    if isinstance(result, Exception):
                        app_logger.error(f"查询 {i+1} 处理失败: {str(result)}")
                        # 创建错误结果
                        processed_results.append(ExtractionResult(
                            query=queries[i],
                            extracted_items=[],
                            summary=f"处理失败: {str(result)}",
                            processing_time=0.0
                        ))
                    else:
                        processed_results.append(result)
                results = processed_results
            else:
                # 顺序处理
                for query in queries:
                    try:
                        result = await self.process_text_query(
                            text, query, language, extraction_mode, config
                        )
                        results.append(result)
                    except Exception as e:
                        app_logger.error(f"查询处理失败: {str(e)}")
                        results.append(ExtractionResult(
                            query=query,
                            extracted_items=[],
                            summary=f"处理失败: {str(e)}",
                            processing_time=0.0
                        ))
            
            app_logger.info(f"批量查询处理完成，共处理 {len(results)} 个查询")
            return results
            
        except Exception as e:
            app_logger.error(f"批量查询处理失败: {str(e)}")
            raise QueryProcessingError(f"批量查询处理失败: {str(e)}")
    
    async def _extract_with_llm(self, doc: Document, config: Optional[PipelineConfigRequest]) -> Document:
        """
        使用 LLM 进行提取

        Args:
            doc: ContextGem 文档
            config: 配置

        Returns:
            Document: 处理后的文档
        """
        try:
            use_concurrency = config.use_concurrency if config else False

            # 执行提取
            processed_doc = self.llm.extract_all(
                doc,
                use_concurrency=use_concurrency,
                max_items_per_call=1 if use_concurrency else 0
            )

            return processed_doc

        except Exception as e:
            app_logger.error(f"LLM 提取失败: {str(e)}")
            raise ContextGemError(f"LLM 提取失败: {str(e)}")
    
    async def _update_llm_config(self, config: PipelineConfigRequest) -> None:
        """
        更新 LLM 配置

        Args:
            config: 新的配置
        """
        try:
            if config.model_name and config.model_name != self.llm.model:
                # 重新创建 LLM 实例
                llm_config = {
                    "model": config.model_name,
                    "api_key": settings.openai_api_key,
                    "role": "extractor_text",
                    "temperature": config.temperature or 0.3,
                    "max_tokens": config.max_tokens or 4096,
                    "output_language": "adapt",
                    "async_limiter": AsyncLimiter(3000, 10)
                }

                # 如果配置了自定义 api_base，添加到配置中
                if settings.openai_base_url:
                    llm_config["api_base"] = settings.openai_base_url

                # 对于 OpenAI 兼容端点，不需要额外的默认配置

                self.llm = DocumentLLM(**llm_config)
            else:
                # 更新现有配置
                if config.temperature is not None:
                    self.llm.temperature = config.temperature
                if config.max_tokens is not None:
                    self.llm.max_tokens = config.max_tokens

        except Exception as e:
            app_logger.error(f"更新 LLM 配置失败: {str(e)}")
            raise ContextGemError(f"更新 LLM 配置失败: {str(e)}")
    
    def _generate_summary(self, extracted_items: List[ExtractedItem], query: str) -> str:
        """
        生成结果摘要
        
        Args:
            extracted_items: 提取的信息项
            query: 原始查询
            
        Returns:
            str: 摘要
        """
        if not extracted_items:
            return f"未找到与查询 '{query}' 相关的信息"
        
        summary_parts = [
            f"根据查询 '{query}' 找到 {len(extracted_items)} 个相关信息项："
        ]
        
        for i, item in enumerate(extracted_items[:3], 1):  # 只显示前3个
            content_preview = item.content[:50000] + "..." if len(item.content) > 50000 else item.content
            summary_parts.append(f"{i}. {content_preview}")
        
        if len(extracted_items) > 3:
            summary_parts.append(f"... 以及其他 {len(extracted_items) - 3} 个信息项")
        
        return "\n".join(summary_parts)
    
    def _get_token_usage(self) -> Dict[str, int]:
        """
        获取令牌使用情况
        
        Returns:
            Dict[str, int]: 令牌使用统计
        """
        # TODO: 实现令牌使用统计
        # 这需要 ContextGem 提供相应的 API
        return {
            "input_tokens": 0,
            "output_tokens": 0,
            "total_tokens": 0
        }

    def get_cost_info(self) -> Dict[str, Any]:
        """
        获取成本信息

        Returns:
            Dict[str, Any]: 成本统计
        """
        # TODO: 实现成本统计
        # 这需要 ContextGem 提供相应的 API
        return {
            "total_cost": 0.0,
            "input_cost": 0.0,
            "output_cost": 0.0,
            "currency": "USD"
        }
