"""
文件处理工具模块

提供各种文件处理相关的工具函数
"""

import os
import mimetypes
from typing import Optional, Dict, Any, List
from pathlib import Path
import hashlib

# 尝试导入 python-magic，如果失败则使用备用方案
try:
    import magic
    HAS_MAGIC = True
except ImportError:
    HAS_MAGIC = False

from app.core.config import settings
from app.utils.logger import app_logger


class FileValidator:
    """文件验证器"""
    
    # MIME 类型映射
    MIME_TYPE_MAPPING = {
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document': '.docx',
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': '.xlsx',
        'application/vnd.ms-excel': '.xls',
        'text/plain': '.txt',
        'text/markdown': '.md',
        'application/octet-stream': None  # 需要进一步检测
    }
    
    @staticmethod
    def validate_file_extension(filename: str) -> bool:
        """
        验证文件扩展名是否支持
        
        Args:
            filename: 文件名
            
        Returns:
            bool: 是否支持
        """
        if not filename:
            return False
        
        file_ext = Path(filename).suffix.lower()
        return file_ext in settings.allowed_extensions
    
    @staticmethod
    def validate_file_size(file_size: int) -> bool:
        """
        验证文件大小是否在限制范围内
        
        Args:
            file_size: 文件大小（字节）
            
        Returns:
            bool: 是否在限制范围内
        """
        return file_size <= settings.max_file_size
    
    @staticmethod
    def detect_file_type(file_path: Path) -> Optional[str]:
        """
        检测文件的真实类型

        Args:
            file_path: 文件路径

        Returns:
            Optional[str]: 检测到的文件扩展名
        """
        try:
            if HAS_MAGIC:
                # 使用 python-magic 检测文件类型
                mime_type = magic.from_file(str(file_path), mime=True)

                # 根据 MIME 类型映射到扩展名
                if mime_type in FileValidator.MIME_TYPE_MAPPING:
                    detected_ext = FileValidator.MIME_TYPE_MAPPING[mime_type]
                    if detected_ext:
                        return detected_ext
            else:
                # 使用 mimetypes 作为备用方案
                mime_type, _ = mimetypes.guess_type(str(file_path))
                if mime_type and mime_type in FileValidator.MIME_TYPE_MAPPING:
                    detected_ext = FileValidator.MIME_TYPE_MAPPING[mime_type]
                    if detected_ext:
                        return detected_ext

            # 如果无法通过 MIME 类型确定，使用文件扩展名
            return file_path.suffix.lower()

        except Exception as e:
            app_logger.warning(f"文件类型检测失败: {str(e)}")
            return file_path.suffix.lower()
    
    @staticmethod
    def validate_file_content(file_path: Path, expected_extension: str) -> bool:
        """
        验证文件内容是否与扩展名匹配
        
        Args:
            file_path: 文件路径
            expected_extension: 期望的扩展名
            
        Returns:
            bool: 是否匹配
        """
        try:
            detected_ext = FileValidator.detect_file_type(file_path)
            return detected_ext == expected_extension.lower()
        except Exception as e:
            app_logger.warning(f"文件内容验证失败: {str(e)}")
            return True  # 验证失败时默认通过


class FileUtils:
    """文件工具类"""
    
    @staticmethod
    def calculate_file_hash(file_path: Path, algorithm: str = 'md5') -> str:
        """
        计算文件哈希值
        
        Args:
            file_path: 文件路径
            algorithm: 哈希算法 (md5, sha1, sha256)
            
        Returns:
            str: 文件哈希值
        """
        hash_func = hashlib.new(algorithm)
        
        try:
            with open(file_path, 'rb') as f:
                for chunk in iter(lambda: f.read(4096), b""):
                    hash_func.update(chunk)
            return hash_func.hexdigest()
        except Exception as e:
            app_logger.error(f"计算文件哈希失败: {str(e)}")
            raise
    
    @staticmethod
    def get_file_info(file_path: Path) -> Dict[str, Any]:
        """
        获取文件的详细信息
        
        Args:
            file_path: 文件路径
            
        Returns:
            Dict[str, Any]: 文件信息
        """
        try:
            stat = file_path.stat()
            
            return {
                'name': file_path.name,
                'size': stat.st_size,
                'extension': file_path.suffix.lower(),
                'created_time': stat.st_ctime,
                'modified_time': stat.st_mtime,
                'is_file': file_path.is_file(),
                'is_readable': os.access(file_path, os.R_OK),
                'mime_type': mimetypes.guess_type(str(file_path))[0],
                'hash_md5': FileUtils.calculate_file_hash(file_path, 'md5')
            }
        except Exception as e:
            app_logger.error(f"获取文件信息失败: {str(e)}")
            raise
    
    @staticmethod
    def ensure_directory(directory: Path) -> None:
        """
        确保目录存在，如果不存在则创建
        
        Args:
            directory: 目录路径
        """
        try:
            directory.mkdir(parents=True, exist_ok=True)
        except Exception as e:
            app_logger.error(f"创建目录失败: {str(e)}")
            raise
    
    @staticmethod
    def clean_filename(filename: str) -> str:
        """
        清理文件名，移除不安全的字符
        
        Args:
            filename: 原始文件名
            
        Returns:
            str: 清理后的文件名
        """
        # 移除或替换不安全的字符
        unsafe_chars = ['<', '>', ':', '"', '|', '?', '*', '\\', '/']
        cleaned = filename
        
        for char in unsafe_chars:
            cleaned = cleaned.replace(char, '_')
        
        # 移除前后空格
        cleaned = cleaned.strip()
        
        # 确保文件名不为空
        if not cleaned:
            cleaned = "unnamed_file"
        
        return cleaned
    
    @staticmethod
    def get_safe_filename(original_filename: str, document_id: str) -> str:
        """
        生成安全的文件名
        
        Args:
            original_filename: 原始文件名
            document_id: 文档ID
            
        Returns:
            str: 安全的文件名
        """
        # 清理原始文件名
        cleaned_name = FileUtils.clean_filename(original_filename)
        
        # 获取文件扩展名
        file_path = Path(cleaned_name)
        extension = file_path.suffix
        name_without_ext = file_path.stem
        
        # 生成新的文件名：document_id + 原始名称 + 扩展名
        safe_filename = f"{document_id}_{name_without_ext}{extension}"
        
        return safe_filename
    
    @staticmethod
    def format_file_size(size_bytes: int) -> str:
        """
        格式化文件大小为人类可读的格式
        
        Args:
            size_bytes: 文件大小（字节）
            
        Returns:
            str: 格式化后的大小
        """
        if size_bytes == 0:
            return "0 B"
        
        size_names = ["B", "KB", "MB", "GB", "TB"]
        i = 0
        size = float(size_bytes)
        
        while size >= 1024.0 and i < len(size_names) - 1:
            size /= 1024.0
            i += 1
        
        return f"{size:.1f} {size_names[i]}"
    
    @staticmethod
    def is_text_file(file_path: Path) -> bool:
        """
        判断文件是否为文本文件
        
        Args:
            file_path: 文件路径
            
        Returns:
            bool: 是否为文本文件
        """
        try:
            # 检查扩展名
            text_extensions = ['.txt', '.md', '.csv', '.json', '.xml', '.html']
            if file_path.suffix.lower() in text_extensions:
                return True
            
            # 检查 MIME 类型
            mime_type, _ = mimetypes.guess_type(str(file_path))
            if mime_type and mime_type.startswith('text/'):
                return True
            
            # 尝试读取文件开头判断是否为文本
            with open(file_path, 'rb') as f:
                chunk = f.read(1024)
                if not chunk:
                    return True  # 空文件视为文本文件
                
                # 检查是否包含空字节（二进制文件的特征）
                if b'\x00' in chunk:
                    return False
                
                # 尝试解码为 UTF-8
                try:
                    chunk.decode('utf-8')
                    return True
                except UnicodeDecodeError:
                    return False
                    
        except Exception as e:
            app_logger.warning(f"判断文本文件类型失败: {str(e)}")
            return False
    
    @staticmethod
    def list_files_in_directory(directory: Path, pattern: str = "*") -> List[Path]:
        """
        列出目录中的文件
        
        Args:
            directory: 目录路径
            pattern: 文件模式（如 "*.txt"）
            
        Returns:
            List[Path]: 文件路径列表
        """
        try:
            if not directory.exists() or not directory.is_dir():
                return []
            
            return list(directory.glob(pattern))
        except Exception as e:
            app_logger.error(f"列出目录文件失败: {str(e)}")
            return []


class DocumentMetadata:
    """文档元数据管理"""
    
    @staticmethod
    def extract_metadata(file_path: Path) -> Dict[str, Any]:
        """
        提取文档元数据
        
        Args:
            file_path: 文件路径
            
        Returns:
            Dict[str, Any]: 元数据字典
        """
        metadata = {}
        
        try:
            # 基本文件信息
            file_info = FileUtils.get_file_info(file_path)
            metadata.update(file_info)
            
            # 根据文件类型提取特定元数据
            extension = file_path.suffix.lower()
            
            if extension == '.docx':
                metadata.update(DocumentMetadata._extract_docx_metadata(file_path))
            elif extension in ['.xlsx', '.xls']:
                metadata.update(DocumentMetadata._extract_excel_metadata(file_path))
            
            return metadata
            
        except Exception as e:
            app_logger.error(f"提取文档元数据失败: {str(e)}")
            return metadata
    
    @staticmethod
    def _extract_docx_metadata(file_path: Path) -> Dict[str, Any]:
        """提取 DOCX 文档元数据"""
        try:
            from docx import Document
            doc = Document(file_path)
            
            core_props = doc.core_properties
            
            return {
                'title': core_props.title or '',
                'author': core_props.author or '',
                'subject': core_props.subject or '',
                'created': core_props.created,
                'modified': core_props.modified,
                'last_modified_by': core_props.last_modified_by or '',
                'revision': core_props.revision,
                'paragraph_count': len(doc.paragraphs),
                'word_count': len(' '.join([p.text for p in doc.paragraphs]).split())
            }
        except Exception as e:
            app_logger.warning(f"提取 DOCX 元数据失败: {str(e)}")
            return {}
    
    @staticmethod
    def _extract_excel_metadata(file_path: Path) -> Dict[str, Any]:
        """提取 Excel 文档元数据"""
        try:
            from openpyxl import load_workbook
            wb = load_workbook(file_path, read_only=True)
            
            props = wb.properties
            
            return {
                'title': props.title or '',
                'creator': props.creator or '',
                'subject': props.subject or '',
                'created': props.created,
                'modified': props.modified,
                'last_modified_by': props.lastModifiedBy or '',
                'sheet_count': len(wb.sheetnames),
                'sheet_names': wb.sheetnames
            }
        except Exception as e:
            app_logger.warning(f"提取 Excel 元数据失败: {str(e)}")
            return {}
