"""
文档处理服务模块

负责处理各种格式的文档，包括解析、预处理和内容提取
"""

import os
import uuid
import asyncio
import re
from typing import Optional, Dict, Any, List, Tuple, BinaryIO
from datetime import datetime
from pathlib import Path
import aiofiles

from fastapi import UploadFile
from docx import Document as DocxDocument
from openpyxl import load_workbook
import pandas as pd

from app.core.config import settings
from app.core.exceptions import (
    DocumentProcessingError,
    UnsupportedFileTypeError,
    FileSizeExceededError
)
from app.api.models.requests import DocumentFormat
from app.api.models.responses import DocumentInfo
from app.utils.logger import app_logger
from app.services.contextgem_converter import ContextGemConverter


class DocumentProcessor:
    """文档处理器类"""
    
    def __init__(self):
        """初始化文档处理器"""
        self.upload_dir = Path(settings.upload_dir)
        self.upload_dir.mkdir(exist_ok=True)

        # 支持的文件扩展名映射
        self.format_mapping = {
            '.docx': DocumentFormat.DOCX,
            '.xlsx': DocumentFormat.XLSX,
            '.xls': DocumentFormat.XLS,
            '.csv': DocumentFormat.CSV,
            '.txt': DocumentFormat.TXT,
            '.md': DocumentFormat.MARKDOWN
        }

        # 初始化 ContextGem 转换器
        self.contextgem_converter = ContextGemConverter()
    
    async def validate_file(self, file: UploadFile) -> None:
        """
        验证上传的文件
        
        Args:
            file: 上传的文件对象
            
        Raises:
            UnsupportedFileTypeError: 不支持的文件类型
            FileSizeExceededError: 文件大小超限
        """
        # 检查文件大小
        if hasattr(file, 'size') and file.size > settings.max_file_size:
            raise FileSizeExceededError(
                f"文件大小 {file.size} 字节超过限制 {settings.max_file_size} 字节"
            )
        
        # 检查文件扩展名
        if file.filename:
            file_ext = Path(file.filename).suffix.lower()
            if file_ext not in self.format_mapping:
                raise UnsupportedFileTypeError(
                    f"不支持的文件格式: {file_ext}。支持的格式: {list(self.format_mapping.keys())}"
                )
    
    async def save_uploaded_file(self, file: UploadFile) -> Tuple[str, Path]:
        """
        保存上传的文件
        
        Args:
            file: 上传的文件对象
            
        Returns:
            Tuple[str, Path]: (文档ID, 文件路径)
        """
        # 生成唯一的文档ID
        document_id = str(uuid.uuid4())
        
        # 获取文件扩展名
        file_ext = Path(file.filename).suffix.lower() if file.filename else ''
        
        # 构建文件路径
        file_path = self.upload_dir / f"{document_id}{file_ext}"
        
        try:
            # 异步保存文件
            async with aiofiles.open(file_path, 'wb') as f:
                content = await file.read()
                await f.write(content)
            
            app_logger.info(f"文件已保存: {file_path}")
            return document_id, file_path
            
        except Exception as e:
            app_logger.error(f"保存文件失败: {str(e)}")
            raise DocumentProcessingError(f"保存文件失败: {str(e)}")
    
    async def extract_text_from_docx(self, file_path: Path) -> str:
        """
        从 DOCX 文件提取文本

        Args:
            file_path: 文件路径

        Returns:
            str: 提取的文本内容
        """
        try:
            doc = DocxDocument(file_path)
            text_content = []

            # 提取段落文本
            for paragraph in doc.paragraphs:
                if paragraph.text.strip():
                    # 检查段落样式
                    style_name = paragraph.style.name if paragraph.style else ""
                    text = paragraph.text.strip()

                    # 根据样式添加格式标记
                    if 'Heading' in style_name:
                        level = style_name.replace('Heading ', '')
                        text_content.append(f"{'#' * int(level) if level.isdigit() else '#'} {text}")
                    elif 'List' in style_name:
                        text_content.append(f"• {text}")
                    else:
                        text_content.append(text)

            # 提取表格内容
            for table in doc.tables:
                text_content.append("\n[表格内容]")
                for i, row in enumerate(table.rows):
                    row_text = []
                    for cell in row.cells:
                        cell_text = cell.text.strip()
                        if cell_text:
                            row_text.append(cell_text)

                    if row_text:
                        if i == 0:  # 表头
                            text_content.append("表头: " + " | ".join(row_text))
                        else:
                            text_content.append("数据: " + " | ".join(row_text))
                text_content.append("[表格结束]\n")

            return '\n'.join(text_content)

        except Exception as e:
            app_logger.error(f"DOCX文件解析失败: {str(e)}")
            raise DocumentProcessingError(f"DOCX文件解析失败: {str(e)}")
    
    async def extract_text_from_excel(self, file_path: Path) -> str:
        """
        从 Excel 文件提取文本

        Args:
            file_path: 文件路径

        Returns:
            str: 提取的文本内容
        """
        try:
            # 使用 pandas 读取 Excel 文件
            df_dict = pd.read_excel(file_path, sheet_name=None)  # 读取所有工作表

            text_content = []
            for sheet_name, sheet_df in df_dict.items():
                text_content.append(f"\n=== 工作表: {sheet_name} ===")

                # 添加列名作为表头
                if not sheet_df.empty:
                    headers = [str(col) for col in sheet_df.columns if str(col) != 'nan']
                    if headers:
                        text_content.append(f"列名: {' | '.join(headers)}")

                    # 添加数据行
                    for index, row in sheet_df.iterrows():
                        # 过滤掉空值
                        row_data = []
                        for i, (col, value) in enumerate(row.items()):
                            if pd.notna(value) and str(value).strip():
                                # 添加列名和值的对应关系
                                row_data.append(f"{col}: {str(value).strip()}")

                        if row_data:
                            text_content.append(" | ".join(row_data))

                    # 添加统计信息
                    text_content.append(f"数据行数: {len(sheet_df)}")
                    text_content.append(f"列数: {len(sheet_df.columns)}")

                    # 对数值列添加统计信息
                    numeric_cols = sheet_df.select_dtypes(include=['number']).columns
                    if len(numeric_cols) > 0:
                        text_content.append("数值列统计:")
                        for col in numeric_cols:
                            if not sheet_df[col].empty:
                                mean_val = sheet_df[col].mean()
                                max_val = sheet_df[col].max()
                                min_val = sheet_df[col].min()
                                text_content.append(f"  {col}: 平均值={mean_val:.2f}, 最大值={max_val}, 最小值={min_val}")

                text_content.append("")  # 工作表之间的分隔

            return '\n'.join(text_content)

        except Exception as e:
            app_logger.error(f"Excel文件解析失败: {str(e)}")
            raise DocumentProcessingError(f"Excel文件解析失败: {str(e)}")

    async def extract_text_from_csv(self, file_path: Path) -> str:
        """
        从 CSV 文件提取文本

        Args:
            file_path: 文件路径

        Returns:
            str: 提取的文本内容
        """
        try:
            # 尝试不同的编码格式读取 CSV
            encodings = ['utf-8', 'gbk', 'gb2312', 'latin1', 'cp1252']
            df = None
            used_encoding = None

            for encoding in encodings:
                try:
                    df = pd.read_csv(file_path, encoding=encoding)
                    used_encoding = encoding
                    app_logger.info(f"成功使用 {encoding} 编码读取 CSV 文件")
                    break
                except UnicodeDecodeError:
                    continue
                except Exception as e:
                    app_logger.warning(f"使用 {encoding} 编码读取 CSV 失败: {str(e)}")
                    continue

            if df is None:
                raise DocumentProcessingError("无法使用任何编码格式读取 CSV 文件")

            text_content = []
            text_content.append("=== CSV 数据 ===")

            # 添加文件信息
            text_content.append(f"编码格式: {used_encoding}")
            text_content.append(f"数据行数: {len(df)}")
            text_content.append(f"列数: {len(df.columns)}")

            # 添加列名作为表头
            if not df.empty:
                headers = [str(col) for col in df.columns if str(col) != 'nan']
                if headers:
                    text_content.append(f"列名: {' | '.join(headers)}")

                    # 分析列的数据类型
                    column_types = []
                    for col in headers:
                        col_type = str(df[col].dtype)
                        non_null_count = df[col].count()
                        column_types.append(f"{col}({col_type}, 非空值:{non_null_count})")
                    text_content.append(f"列类型: {' | '.join(column_types)}")

                # 添加数据行（限制显示前100行以避免过长）
                max_rows = min(100, len(df))
                text_content.append(f"\n--- 数据内容 (显示前{max_rows}行) ---")

                for index, row in df.head(max_rows).iterrows():
                    # 过滤掉空值
                    row_data = []
                    for col, value in row.items():
                        if pd.notna(value) and str(value).strip():
                            # 添加列名和值的对应关系
                            row_data.append(f"{col}: {str(value).strip()}")

                    if row_data:
                        text_content.append(" | ".join(row_data))

                # 添加统计信息
                text_content.append(f"\n--- 统计信息 ---")

                # 对数值列添加统计信息
                numeric_cols = df.select_dtypes(include=['number']).columns
                if len(numeric_cols) > 0:
                    text_content.append("数值列统计:")
                    for col in numeric_cols:
                        if not df[col].empty:
                            mean_val = df[col].mean()
                            max_val = df[col].max()
                            min_val = df[col].min()
                            std_val = df[col].std()
                            text_content.append(f"  {col}: 平均值={mean_val:.2f}, 最大值={max_val}, 最小值={min_val}, 标准差={std_val:.2f}")

                # 对文本列添加统计信息
                text_cols = df.select_dtypes(include=['object']).columns
                if len(text_cols) > 0:
                    text_content.append("文本列统计:")
                    for col in text_cols:
                        unique_count = df[col].nunique()
                        most_common = df[col].mode().iloc[0] if not df[col].mode().empty else "无"
                        text_content.append(f"  {col}: 唯一值数量={unique_count}, 最常见值={most_common}")

                # 如果数据行数超过显示限制，添加说明
                if len(df) > max_rows:
                    text_content.append(f"\n注意: 文件共有 {len(df)} 行数据，仅显示前 {max_rows} 行")

            return '\n'.join(text_content)

        except Exception as e:
            app_logger.error(f"CSV文件解析失败: {str(e)}")
            raise DocumentProcessingError(f"CSV文件解析失败: {str(e)}")
    
    async def extract_text_from_txt(self, file_path: Path) -> str:
        """
        从文本文件提取内容

        Args:
            file_path: 文件路径

        Returns:
            str: 文件内容
        """
        try:
            async with aiofiles.open(file_path, 'r', encoding='utf-8') as f:
                content = await f.read()
                return self._process_text_content(content, file_path.suffix.lower())

        except UnicodeDecodeError:
            # 尝试其他编码
            encodings = ['gbk', 'gb2312', 'latin1', 'cp1252']
            for encoding in encodings:
                try:
                    async with aiofiles.open(file_path, 'r', encoding=encoding) as f:
                        content = await f.read()
                        return self._process_text_content(content, file_path.suffix.lower())
                except UnicodeDecodeError:
                    continue

            app_logger.error(f"无法解码文本文件: {file_path}")
            raise DocumentProcessingError(f"无法解码文本文件，尝试了多种编码格式")
        except Exception as e:
            app_logger.error(f"文本文件解析失败: {str(e)}")
            raise DocumentProcessingError(f"文本文件解析失败: {str(e)}")

    def _process_text_content(self, content: str, file_extension: str) -> str:
        """
        处理文本内容，根据文件类型进行特殊处理

        Args:
            content: 原始文本内容
            file_extension: 文件扩展名

        Returns:
            str: 处理后的文本内容
        """
        if file_extension == '.md':
            return self._process_markdown_content(content)
        else:
            return content

    def _process_markdown_content(self, content: str) -> str:
        """
        处理 Markdown 内容，提取结构化信息

        Args:
            content: Markdown 内容

        Returns:
            str: 处理后的文本
        """
        lines = content.split('\n')
        processed_lines = []
        in_table = False
        table_headers = []

        for line in lines:
            line = line.strip()
            if not line:
                processed_lines.append("")
                continue

            # 处理标题
            if line.startswith('#'):
                level = len(line) - len(line.lstrip('#'))
                title = line.lstrip('#').strip()
                processed_lines.append(f"{'=' * level} {title} {'=' * level}")

            # 处理列表
            elif line.startswith(('- ', '* ', '+ ')):
                item = line[2:].strip()
                processed_lines.append(f"• {item}")

            elif line.startswith(('1. ', '2. ', '3. ', '4. ', '5. ', '6. ', '7. ', '8. ', '9. ')):
                item = line[3:].strip()
                processed_lines.append(f"◦ {item}")

            # 处理表格
            elif '|' in line and not in_table:
                # 表格开始
                in_table = True
                table_headers = [cell.strip() for cell in line.split('|') if cell.strip()]
                processed_lines.append(f"[表格] 列名: {' | '.join(table_headers)}")

            elif '|' in line and in_table:
                # 检查是否是分隔行
                if all(c in '-|: ' for c in line):
                    continue  # 跳过分隔行

                # 表格数据行
                cells = [cell.strip() for cell in line.split('|') if cell.strip()]
                if cells and len(cells) == len(table_headers):
                    row_data = []
                    for header, cell in zip(table_headers, cells):
                        row_data.append(f"{header}: {cell}")
                    processed_lines.append(f"[表格数据] {' | '.join(row_data)}")

            # 处理代码块
            elif line.startswith('```'):
                if line == '```':
                    processed_lines.append("[代码块结束]")
                else:
                    lang = line[3:].strip()
                    processed_lines.append(f"[代码块开始: {lang}]")

            # 处理引用
            elif line.startswith('>'):
                quote = line[1:].strip()
                processed_lines.append(f"引用: {quote}")

            # 处理链接和图片
            elif '[' in line and '](' in line:
                # 简单的链接/图片处理
                import re
                # 提取链接文本
                links = re.findall(r'\[([^\]]+)\]\([^)]+\)', line)
                if links:
                    for link_text in links:
                        processed_lines.append(f"链接: {link_text}")
                processed_lines.append(line)

            else:
                # 普通文本
                if in_table and '|' not in line:
                    in_table = False  # 表格结束
                processed_lines.append(line)

        return '\n'.join(processed_lines)
    
    async def extract_text_content(self, file_path: Path, document_format: DocumentFormat) -> str:
        """
        根据文档格式提取文本内容

        Args:
            file_path: 文件路径
            document_format: 文档格式

        Returns:
            str: 提取的文本内容
        """
        if document_format == DocumentFormat.DOCX:
            return await self.extract_text_from_docx(file_path)
        elif document_format in [DocumentFormat.XLSX, DocumentFormat.XLS]:
            return await self.extract_text_from_excel(file_path)
        elif document_format == DocumentFormat.CSV:
            return await self.extract_text_from_csv(file_path)
        elif document_format in [DocumentFormat.TXT, DocumentFormat.MARKDOWN]:
            return await self.extract_text_from_txt(file_path)
        else:
            raise UnsupportedFileTypeError(f"不支持的文档格式: {document_format}")
    
    async def analyze_document(self, file_path: Path, document_format: DocumentFormat) -> Dict[str, Any]:
        """
        分析文档并提取元数据

        Args:
            file_path: 文件路径
            document_format: 文档格式

        Returns:
            Dict[str, Any]: 文档分析结果
        """
        try:
            # 获取文件基本信息
            file_stat = file_path.stat()

            # 提取文本内容
            text_content = await self.extract_text_content(file_path, document_format)

            # 计算基本统计信息
            analysis_result = self._analyze_text_content(text_content)

            # 简单的语言检测（基于字符特征）
            language = self._detect_language(text_content)

            # 提取文档特定的元数据
            document_metadata = await self._extract_document_metadata(file_path, document_format)

            return {
                'text_content': text_content,
                'file_size': file_stat.st_size,
                'word_count': analysis_result['word_count'],
                'language': language,
                'character_count': analysis_result['character_count'],
                'line_count': analysis_result['line_count'],
                'paragraph_count': analysis_result['paragraph_count'],
                'sentence_count': analysis_result['sentence_count'],
                'unique_words': analysis_result['unique_words'],
                'reading_time_minutes': analysis_result['reading_time_minutes'],
                'document_structure': analysis_result['document_structure'],
                'metadata': document_metadata
            }

        except Exception as e:
            app_logger.error(f"文档分析失败: {str(e)}")
            raise DocumentProcessingError(f"文档分析失败: {str(e)}")

    def _analyze_text_content(self, text_content: str) -> Dict[str, Any]:
        """
        分析文本内容的详细统计信息

        Args:
            text_content: 文本内容

        Returns:
            Dict[str, Any]: 分析结果
        """
        if not text_content:
            return {
                'word_count': 0,
                'character_count': 0,
                'line_count': 0,
                'paragraph_count': 0,
                'sentence_count': 0,
                'unique_words': 0,
                'reading_time_minutes': 0,
                'document_structure': {}
            }

        lines = text_content.splitlines()
        paragraphs = [p.strip() for p in text_content.split('\n\n') if p.strip()]

        # 句子计数（简单的中英文句子分割）
        import re
        sentences = re.split(r'[。！？.!?]+', text_content)
        sentences = [s.strip() for s in sentences if s.strip()]

        # 词汇统计
        words = text_content.split()
        unique_words = len(set(words))

        # 阅读时间估算（假设每分钟200字）
        reading_time = len(words) / 200

        # 文档结构分析
        structure = self._analyze_document_structure(text_content)

        return {
            'word_count': len(words),
            'character_count': len(text_content),
            'line_count': len(lines),
            'paragraph_count': len(paragraphs),
            'sentence_count': len(sentences),
            'unique_words': unique_words,
            'reading_time_minutes': round(reading_time, 1),
            'document_structure': structure
        }

    def _analyze_document_structure(self, text_content: str) -> Dict[str, Any]:
        """
        分析文档结构

        Args:
            text_content: 文本内容

        Returns:
            Dict[str, Any]: 结构分析结果
        """
        structure = {
            'headings': [],
            'lists': 0,
            'tables': 0,
            'code_blocks': 0,
            'links': 0,
            'images': 0
        }

        lines = text_content.splitlines()

        for line in lines:
            line = line.strip()

            # 检测标题
            if line.startswith('#') or line.startswith('='):
                level = len(line) - len(line.lstrip('#='))
                title = line.lstrip('#=').strip()
                structure['headings'].append({
                    'level': level,
                    'title': title
                })

            # 检测列表
            elif line.startswith(('• ', '◦ ', '- ', '* ', '+ ')) or re.match(r'^\d+\.', line):
                structure['lists'] += 1

            # 检测表格
            elif '[表格' in line or '|' in line:
                structure['tables'] += 1

            # 检测代码块
            elif '[代码块' in line or line.startswith('```'):
                structure['code_blocks'] += 1

            # 检测链接
            elif 'http' in line or '[' in line and '](' in line:
                structure['links'] += 1

            # 检测图片引用
            elif '![' in line or '图片' in line or '图像' in line:
                structure['images'] += 1

        return structure

    async def _extract_document_metadata(self, file_path: Path, document_format: DocumentFormat) -> Dict[str, Any]:
        """
        提取文档特定的元数据

        Args:
            file_path: 文件路径
            document_format: 文档格式

        Returns:
            Dict[str, Any]: 元数据
        """
        metadata = {}

        try:
            if document_format == DocumentFormat.DOCX:
                # 提取 DOCX 元数据
                doc = DocxDocument(file_path)
                core_props = doc.core_properties

                metadata.update({
                    'title': core_props.title or '',
                    'author': core_props.author or '',
                    'subject': core_props.subject or '',
                    'created': core_props.created.isoformat() if core_props.created else None,
                    'modified': core_props.modified.isoformat() if core_props.modified else None,
                    'last_modified_by': core_props.last_modified_by or '',
                    'revision': core_props.revision,
                    'paragraph_count': len(doc.paragraphs),
                    'table_count': len(doc.tables)
                })

            elif document_format in [DocumentFormat.XLSX, DocumentFormat.XLS]:
                # 提取 Excel 元数据
                try:
                    from openpyxl import load_workbook
                    wb = load_workbook(file_path, read_only=True)
                    props = wb.properties

                    metadata.update({
                        'title': props.title or '',
                        'creator': props.creator or '',
                        'subject': props.subject or '',
                        'created': props.created.isoformat() if props.created else None,
                        'modified': props.modified.isoformat() if props.modified else None,
                        'last_modified_by': props.lastModifiedBy or '',
                        'sheet_count': len(wb.sheetnames),
                        'sheet_names': wb.sheetnames
                    })

                    # 添加工作表统计信息
                    sheet_stats = []
                    for sheet_name in wb.sheetnames:
                        sheet = wb[sheet_name]
                        sheet_stats.append({
                            'name': sheet_name,
                            'max_row': sheet.max_row,
                            'max_column': sheet.max_column
                        })
                    metadata['sheet_statistics'] = sheet_stats

                except Exception as e:
                    app_logger.warning(f"无法提取Excel元数据: {str(e)}")

            else:
                # 文本文件和 Markdown 文件
                metadata.update({
                    'encoding': 'utf-8',  # 假设使用 UTF-8 编码
                    'file_type': document_format.value
                })

        except Exception as e:
            app_logger.warning(f"提取文档元数据失败: {str(e)}")

        return metadata
    
    def _detect_language(self, text: str) -> str:
        """
        简单的语言检测
        
        Args:
            text: 文本内容
            
        Returns:
            str: 检测到的语言代码
        """
        if not text:
            return "unknown"
        
        # 统计中文字符
        chinese_chars = sum(1 for char in text if '\u4e00' <= char <= '\u9fff')
        total_chars = len(text)
        
        if total_chars == 0:
            return "unknown"
        
        chinese_ratio = chinese_chars / total_chars
        
        if chinese_ratio > 0.3:
            return "zh"
        else:
            return "en"
    
    async def process_uploaded_file(self, file: UploadFile) -> DocumentInfo:
        """
        处理上传的文件
        
        Args:
            file: 上传的文件对象
            
        Returns:
            DocumentInfo: 处理后的文档信息
        """
        start_time = datetime.now()
        
        try:
            # 验证文件
            await self.validate_file(file)
            
            # 保存文件
            document_id, file_path = await self.save_uploaded_file(file)
            
            # 获取文档格式
            file_ext = file_path.suffix.lower()
            document_format = self.format_mapping[file_ext]
            
            # 分析文档
            analysis_result = await self.analyze_document(file_path, document_format)
            
            # 创建文档信息对象
            document_info = DocumentInfo(
                document_id=document_id,
                filename=file.filename or "unknown",
                format=document_format.value,
                size=analysis_result['file_size'],
                upload_time=start_time,
                word_count=analysis_result['word_count'],
                language=analysis_result['language']
            )
            
            app_logger.info(f"文档处理完成: {document_id}")
            return document_info
            
        except Exception as e:
            app_logger.error(f"文档处理失败: {str(e)}")
            raise
    
    async def get_document_content(self, document_id: str) -> str:
        """
        获取文档的文本内容
        
        Args:
            document_id: 文档ID
            
        Returns:
            str: 文档文本内容
        """
        # 查找文档文件
        for file_path in self.upload_dir.glob(f"{document_id}.*"):
            file_ext = file_path.suffix.lower()
            if file_ext in self.format_mapping:
                document_format = self.format_mapping[file_ext]
                return await self.extract_text_content(file_path, document_format)
        
        raise DocumentProcessingError(f"未找到文档: {document_id}")
    
    async def delete_document(self, document_id: str) -> bool:
        """
        删除文档文件

        Args:
            document_id: 文档ID

        Returns:
            bool: 是否删除成功
        """
        try:
            for file_path in self.upload_dir.glob(f"{document_id}.*"):
                file_path.unlink()
                app_logger.info(f"文档已删除: {document_id}")
                return True
            return False
        except Exception as e:
            app_logger.error(f"删除文档失败: {str(e)}")
            return False

    async def get_contextgem_document(self, document_id: str):
        """
        获取文档的 ContextGem Document 对象

        Args:
            document_id: 文档ID

        Returns:
            Document: ContextGem 文档对象
        """
        try:
            # 查找文档文件
            for file_path in self.upload_dir.glob(f"{document_id}.*"):
                file_ext = file_path.suffix.lower()

                # 如果是支持 ContextGem 转换的格式
                if self.contextgem_converter.is_supported_format(file_path):
                    return await self.contextgem_converter.convert_file_to_contextgem(file_path)

                # 对于其他格式，创建简单的 ContextGem 文档
                elif file_ext in self.format_mapping:
                    from contextgem import Document
                    document_format = self.format_mapping[file_ext]
                    text_content = await self.extract_text_content(file_path, document_format)

                    doc = Document(raw_text=text_content)

                    # 尝试添加元数据（如果支持的话）
                    try:
                        if hasattr(doc, 'metadata'):
                            doc.metadata = {
                                'document_id': document_id,
                                'source_file': str(file_path),
                                'document_type': document_format.value
                            }
                    except Exception as e:
                        app_logger.warning(f"无法设置文档元数据: {e}")

                    return doc

            raise DocumentProcessingError(f"未找到文档: {document_id}")

        except Exception as e:
            app_logger.error(f"获取 ContextGem 文档失败: {str(e)}")
            raise DocumentProcessingError(f"获取 ContextGem 文档失败: {str(e)}")
