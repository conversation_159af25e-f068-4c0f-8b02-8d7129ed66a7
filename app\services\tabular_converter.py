"""
表格数据转换器

专门处理 CSV 和 Excel 文件，将其转换为适合 ContextGem 查询的格式
"""

import asyncio
from typing import Dict, Any, List, Optional, Tuple
from pathlib import Path
import pandas as pd
from contextgem import Document

from app.core.config import settings
from app.core.exceptions import DocumentProcessingError
from app.utils.logger import app_logger


class TabularConverter:
    """表格数据转换器 - 专门处理 CSV 和 Excel 文件"""

    def __init__(self):
        """初始化转换器"""
        self.supported_formats = ['.csv', '.xlsx', '.xls']
        # 设置 pandas 显示选项
        pd.set_option('display.max_columns', None)
        pd.set_option('display.width', None)
        pd.set_option('display.max_colwidth', 100)
    
    async def convert_csv_to_contextgem(self, file_path: Path) -> Document:
        """
        将 CSV 文件转换为 ContextGem Document

        Args:
            file_path: CSV 文件路径

        Returns:
            Document: ContextGem 文档对象
        """
        try:
            app_logger.info(f"开始转换 CSV 文件: {file_path}")

            # 读取 CSV 数据
            df, encoding = await self._read_csv_with_encoding(file_path)
            
            # 生成结构化文本内容
            structured_text = await self._generate_structured_text_from_dataframe(
                df, file_type="CSV", encoding=encoding
            )
            
            # 创建 ContextGem 文档
            contextgem_doc = Document(raw_text=structured_text)
            
            # 添加元数据
            try:
                if hasattr(contextgem_doc, 'metadata'):
                    contextgem_doc.metadata = {
                        'file_type': 'CSV',
                        'encoding': encoding,
                        'rows': len(df),
                        'columns': len(df.columns),
                        'column_names': list(df.columns),
                        'data_types': {col: str(dtype) for col, dtype in df.dtypes.items()}
                    }
            except Exception as e:
                app_logger.warning(f"无法设置 CSV 文档元数据: {e}")

            app_logger.info(f"CSV 转换完成: {len(structured_text)} 字符")
            return contextgem_doc

        except Exception as e:
            app_logger.error(f"CSV 转换失败: {str(e)}")
            raise DocumentProcessingError(f"CSV 转换失败: {str(e)}")
    
    async def convert_excel_to_contextgem(self, file_path: Path) -> Document:
        """
        将 Excel 文件转换为 ContextGem Document

        Args:
            file_path: Excel 文件路径

        Returns:
            Document: ContextGem 文档对象
        """
        try:
            app_logger.info(f"开始转换 Excel 文件: {file_path}")

            # 读取 Excel 数据（所有工作表）
            df_dict = pd.read_excel(file_path, sheet_name=None)
            
            # 生成结构化文本内容
            structured_text = await self._generate_structured_text_from_excel(df_dict)
            
            # 创建 ContextGem 文档
            contextgem_doc = Document(raw_text=structured_text)
            
            # 添加元数据
            try:
                if hasattr(contextgem_doc, 'metadata'):
                    total_rows = sum(len(df) for df in df_dict.values())
                    contextgem_doc.metadata = {
                        'file_type': 'Excel',
                        'sheet_count': len(df_dict),
                        'sheet_names': list(df_dict.keys()),
                        'total_rows': total_rows,
                        'sheets_info': {
                            name: {
                                'rows': len(df),
                                'columns': len(df.columns),
                                'column_names': list(df.columns)
                            }
                            for name, df in df_dict.items()
                        }
                    }
            except Exception as e:
                app_logger.warning(f"无法设置 Excel 文档元数据: {e}")

            app_logger.info(f"Excel 转换完成: {len(structured_text)} 字符")
            return contextgem_doc

        except Exception as e:
            app_logger.error(f"Excel 转换失败: {str(e)}")
            raise DocumentProcessingError(f"Excel 转换失败: {str(e)}")
    
    async def _read_csv_with_encoding(self, file_path: Path) -> Tuple[pd.DataFrame, str]:
        """
        尝试不同编码读取 CSV 文件

        Args:
            file_path: CSV 文件路径

        Returns:
            Tuple[pd.DataFrame, str]: (数据框, 使用的编码)
        """
        encodings = ['utf-8', 'gbk', 'gb2312', 'latin1', 'cp1252']
        
        for encoding in encodings:
            try:
                df = pd.read_csv(file_path, encoding=encoding)
                app_logger.info(f"成功使用 {encoding} 编码读取 CSV 文件")
                return df, encoding
            except UnicodeDecodeError:
                continue
            except Exception as e:
                app_logger.warning(f"使用 {encoding} 编码读取 CSV 失败: {str(e)}")
                continue
        
        raise DocumentProcessingError("无法使用任何编码格式读取 CSV 文件")
    
    async def _generate_structured_text_from_dataframe(
        self, 
        df: pd.DataFrame, 
        file_type: str = "CSV", 
        encoding: str = "utf-8"
    ) -> str:
        """
        从 DataFrame 生成结构化文本

        Args:
            df: pandas DataFrame
            file_type: 文件类型
            encoding: 编码格式

        Returns:
            str: 结构化文本内容
        """
        text_content = []
        text_content.append(f"=== {file_type} 数据分析 ===")
        
        # 基本信息
        text_content.append(f"文件类型: {file_type}")
        if encoding:
            text_content.append(f"编码格式: {encoding}")
        text_content.append(f"数据行数: {len(df)}")
        text_content.append(f"列数: {len(df.columns)}")
        
        # 列信息
        if not df.empty:
            headers = [str(col) for col in df.columns if str(col) != 'nan']
            if headers:
                text_content.append(f"\n--- 列信息 ---")
                text_content.append(f"列名: {' | '.join(headers)}")
                
                # 分析列的数据类型和基本统计
                column_info = []
                for col in headers:
                    col_type = str(df[col].dtype)
                    non_null_count = df[col].count()
                    null_count = df[col].isnull().sum()
                    column_info.append(f"{col}(类型:{col_type}, 非空:{non_null_count}, 空值:{null_count})")
                text_content.append(f"列详情: {' | '.join(column_info)}")

            # 数据预览（前20行）
            max_preview_rows = min(20, len(df))
            text_content.append(f"\n--- 数据预览 (前{max_preview_rows}行) ---")
            
            for idx, (index, row) in enumerate(df.head(max_preview_rows).iterrows()):
                row_data = []
                for col, value in row.items():
                    if pd.notna(value) and str(value).strip():
                        row_data.append(f"{col}: {str(value).strip()}")
                
                if row_data:
                    text_content.append(f"第{idx+1}行: {' | '.join(row_data)}")

            # 统计分析
            text_content.append(f"\n--- 统计分析 ---")
            
            # 数值列统计
            numeric_cols = df.select_dtypes(include=['number']).columns
            if len(numeric_cols) > 0:
                text_content.append("数值列统计:")
                for col in numeric_cols:
                    if not df[col].empty:
                        stats = df[col].describe()
                        text_content.append(
                            f"  {col}: 平均值={stats['mean']:.2f}, "
                            f"中位数={stats['50%']:.2f}, "
                            f"最大值={stats['max']}, "
                            f"最小值={stats['min']}, "
                            f"标准差={stats['std']:.2f}"
                        )

            # 文本列统计
            text_cols = df.select_dtypes(include=['object']).columns
            if len(text_cols) > 0:
                text_content.append("文本列统计:")
                for col in text_cols:
                    unique_count = df[col].nunique()
                    most_common = df[col].mode().iloc[0] if not df[col].mode().empty else "无"
                    text_content.append(f"  {col}: 唯一值数量={unique_count}, 最常见值={most_common}")

            # 完整数据（如果行数不多）
            if len(df) <= 100:
                text_content.append(f"\n--- 完整数据 ---")
                for idx, (index, row) in enumerate(df.iterrows()):
                    row_data = []
                    for col, value in row.items():
                        if pd.notna(value) and str(value).strip():
                            row_data.append(f"{col}: {str(value).strip()}")
                    
                    if row_data:
                        text_content.append(f"数据行{idx+1}: {' | '.join(row_data)}")
            else:
                text_content.append(f"\n注意: 文件共有 {len(df)} 行数据，完整数据请查看原文件")

        return '\n'.join(text_content)
    
    async def _generate_structured_text_from_excel(self, df_dict: Dict[str, pd.DataFrame]) -> str:
        """
        从 Excel 工作表字典生成结构化文本

        Args:
            df_dict: 工作表名称到 DataFrame 的映射

        Returns:
            str: 结构化文本内容
        """
        text_content = []
        text_content.append("=== Excel 文件数据分析 ===")
        text_content.append(f"工作表数量: {len(df_dict)}")
        text_content.append(f"工作表名称: {', '.join(df_dict.keys())}")
        
        for sheet_name, sheet_df in df_dict.items():
            text_content.append(f"\n{'='*50}")
            text_content.append(f"工作表: {sheet_name}")
            text_content.append(f"{'='*50}")
            
            # 为每个工作表生成结构化文本
            sheet_text = await self._generate_structured_text_from_dataframe(
                sheet_df, file_type=f"Excel工作表({sheet_name})", encoding=None
            )
            text_content.append(sheet_text)
        
        return '\n'.join(text_content)
    
    async def convert_file_to_contextgem(self, file_path: Path) -> Document:
        """
        根据文件类型转换为 ContextGem Document

        Args:
            file_path: 文件路径

        Returns:
            Document: ContextGem 文档对象
        """
        file_ext = file_path.suffix.lower()

        if file_ext == '.csv':
            return await self.convert_csv_to_contextgem(file_path)
        elif file_ext in ['.xlsx', '.xls']:
            return await self.convert_excel_to_contextgem(file_path)
        else:
            raise DocumentProcessingError(f"不支持的表格文件格式: {file_ext}")

    def get_supported_formats(self) -> List[str]:
        """
        获取支持的文件格式列表

        Returns:
            List[str]: 支持的文件格式列表
        """
        return self.supported_formats.copy()
    
    def is_supported_format(self, file_path: Path) -> bool:
        """
        检查文件格式是否支持
        
        Args:
            file_path: 文件路径
            
        Returns:
            bool: 是否支持
        """
        return file_path.suffix.lower() in self.supported_formats
