"""
API 响应数据模型

定义所有 API 端点的响应数据结构
"""

from typing import Optional, List, Dict, Any, Union
from pydantic import BaseModel, Field
from datetime import datetime
from enum import Enum


class ProcessingStatus(str, Enum):
    """处理状态"""
    SUCCESS = "success"
    FAILED = "failed"
    PROCESSING = "processing"
    PARTIAL = "partial"


class ExtractedItem(BaseModel):
    """提取的信息项"""
    content: str = Field(..., description="提取的内容")
    confidence: Optional[float] = Field(None, ge=0.0, le=1.0, description="置信度")
    source_reference: Optional[str] = Field(None, description="来源引用")
    metadata: Optional[Dict[str, Any]] = Field(default_factory=dict, description="元数据")


class ExtractionResult(BaseModel):
    """信息提取结果"""
    query: str = Field(..., description="原始查询")
    extracted_items: List[ExtractedItem] = Field(default_factory=list, description="提取的信息项")
    summary: Optional[str] = Field(None, description="结果摘要")
    processing_time: Optional[float] = Field(None, description="处理时间（秒）")
    token_usage: Optional[Dict[str, int]] = Field(default_factory=dict, description="令牌使用情况")


class DocumentInfo(BaseModel):
    """文档信息"""
    document_id: str = Field(..., description="文档ID")
    filename: str = Field(..., description="文件名")
    format: str = Field(..., description="文档格式")
    size: int = Field(..., description="文件大小（字节）")
    upload_time: datetime = Field(..., description="上传时间")
    page_count: Optional[int] = Field(None, description="页数")
    word_count: Optional[int] = Field(None, description="字数")
    language: Optional[str] = Field(None, description="检测到的语言")


class DocumentUploadResponse(BaseModel):
    """文档上传响应"""
    status: ProcessingStatus = Field(..., description="处理状态")
    message: str = Field(..., description="响应消息")
    document_info: Optional[DocumentInfo] = Field(None, description="文档信息")
    processing_time: Optional[float] = Field(None, description="处理时间（秒）")
    errors: Optional[List[str]] = Field(default_factory=list, description="错误信息")


class QueryResponse(BaseModel):
    """查询响应"""
    status: ProcessingStatus = Field(..., description="处理状态")
    message: str = Field(..., description="响应消息")
    results: List[ExtractionResult] = Field(default_factory=list, description="提取结果")
    total_processing_time: Optional[float] = Field(None, description="总处理时间（秒）")
    cost_info: Optional[Dict[str, Any]] = Field(default_factory=dict, description="成本信息")
    errors: Optional[List[str]] = Field(default_factory=list, description="错误信息")


class BatchQueryResponse(BaseModel):
    """批量查询响应"""
    status: ProcessingStatus = Field(..., description="处理状态")
    message: str = Field(..., description="响应消息")
    results: List[ExtractionResult] = Field(default_factory=list, description="所有查询的提取结果")
    successful_queries: int = Field(default=0, description="成功处理的查询数量")
    failed_queries: int = Field(default=0, description="失败的查询数量")
    total_processing_time: Optional[float] = Field(None, description="总处理时间（秒）")
    cost_info: Optional[Dict[str, Any]] = Field(default_factory=dict, description="成本信息")
    errors: Optional[List[str]] = Field(default_factory=list, description="错误信息")


class HealthResponse(BaseModel):
    """健康检查响应"""
    status: str = Field(..., description="服务状态")
    service: str = Field(..., description="服务名称")
    version: str = Field(..., description="服务版本")
    timestamp: datetime = Field(default_factory=datetime.now, description="检查时间")
    dependencies: Optional[Dict[str, str]] = Field(default_factory=dict, description="依赖服务状态")


class ErrorResponse(BaseModel):
    """错误响应"""
    error: str = Field(..., description="错误消息")
    error_code: Optional[str] = Field(None, description="错误代码")
    details: Optional[Dict[str, Any]] = Field(default_factory=dict, description="错误详情")
    timestamp: datetime = Field(default_factory=datetime.now, description="错误时间")
    request_id: Optional[str] = Field(None, description="请求ID")


class ServiceInfo(BaseModel):
    """服务信息响应"""
    service: str = Field(..., description="服务名称")
    version: str = Field(..., description="服务版本")
    status: str = Field(..., description="服务状态")
    message: str = Field(..., description="状态消息")
    features: List[str] = Field(default_factory=list, description="支持的功能")
    supported_formats: List[str] = Field(default_factory=list, description="支持的文档格式")
    api_endpoints: List[str] = Field(default_factory=list, description="可用的API端点")


class DocumentListResponse(BaseModel):
    """文档列表响应"""
    status: ProcessingStatus = Field(..., description="处理状态")
    message: str = Field(..., description="响应消息")
    documents: List[DocumentInfo] = Field(default_factory=list, description="文档列表")
    total_count: int = Field(default=0, description="文档总数")
    page: Optional[int] = Field(None, description="当前页码")
    page_size: Optional[int] = Field(None, description="每页大小")


class ProcessingStats(BaseModel):
    """处理统计信息"""
    total_documents_processed: int = Field(default=0, description="已处理文档总数")
    total_queries_processed: int = Field(default=0, description="已处理查询总数")
    average_processing_time: Optional[float] = Field(None, description="平均处理时间")
    success_rate: Optional[float] = Field(None, description="成功率")
    total_cost: Optional[float] = Field(None, description="总成本")
    uptime: Optional[str] = Field(None, description="运行时间")
