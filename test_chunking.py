#!/usr/bin/env python3
"""
测试分块处理功能的脚本
"""

import asyncio
import sys
import os

# 添加项目根目录到 Python 路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app.services.contextgem_query_processor import ContextGemQueryProcessor
from app.api.models.requests import QueryLanguage, ExtractionMode


async def test_chunking():
    """测试分块处理功能"""
    
    # 创建一个长文本用于测试
    test_text = """
    人工智能（Artificial Intelligence，简称AI）是计算机科学的一个分支，它企图了解智能的实质，并生产出一种新的能以人类智能相似的方式做出反应的智能机器。该领域的研究包括机器人、语言识别、图像识别、自然语言处理和专家系统等。

    人工智能从诞生以来，理论和技术日益成熟，应用领域也不断扩大。可以设想，未来人工智能带来的科技产品，将会是人类智慧的"容器"。人工智能可以对人的意识、思维的信息过程的模拟。人工智能不是人的智能，但能像人那样思考、也可能超过人的智能。

    机器学习是人工智能的一个重要分支，它是一种通过算法使机器能够自动学习和改进的技术。机器学习算法通过分析大量数据来识别模式，并使用这些模式来做出预测或决策。常见的机器学习类型包括监督学习、无监督学习和强化学习。

    深度学习是机器学习的一个子集，它使用人工神经网络来模拟人脑的工作方式。深度学习在图像识别、语音识别、自然语言处理等领域取得了突破性进展。卷积神经网络（CNN）在图像处理方面表现出色，而循环神经网络（RNN）和长短期记忆网络（LSTM）在序列数据处理方面很有效。

    自然语言处理（NLP）是人工智能的另一个重要领域，它致力于让计算机理解、解释和生成人类语言。NLP技术包括文本分析、情感分析、机器翻译、问答系统等。近年来，基于Transformer架构的大型语言模型如GPT、BERT等在NLP任务中取得了显著成果。

    计算机视觉是使计算机能够从数字图像或视频中获取高层次理解的技术。它包括图像分类、目标检测、图像分割、人脸识别等任务。深度学习的发展极大地推动了计算机视觉技术的进步。

    机器人技术结合了人工智能、机械工程、电子工程等多个学科。现代机器人不仅能执行预编程的任务，还能通过AI技术适应环境变化，进行自主决策。服务机器人、工业机器人、医疗机器人等在各个领域发挥着重要作用。

    专家系统是早期人工智能的重要应用，它通过模拟人类专家的决策过程来解决特定领域的问题。虽然现在被更先进的机器学习方法所取代，但专家系统的思想仍然影响着现代AI系统的设计。

    人工智能的应用领域非常广泛，包括医疗诊断、金融分析、自动驾驶、智能家居、教育、娱乐等。在医疗领域，AI可以帮助医生进行疾病诊断、药物发现和个性化治疗。在金融领域，AI用于风险评估、算法交易和欺诈检测。

    然而，人工智能的发展也带来了一些挑战和担忧。包括就业替代、隐私保护、算法偏见、安全风险等问题。如何确保AI技术的安全、公平和可控发展，是当前面临的重要课题。

    未来，人工智能将继续快速发展，可能会在更多领域实现突破。通用人工智能（AGI）的实现仍然是一个长远目标，但随着技术的不断进步，我们正在逐步接近这个目标。人工智能将继续改变我们的生活方式和工作方式，为人类社会带来更多的便利和可能性。
    """ * 50  # 重复50次以创建一个长文本

    print(f"测试文本长度: {len(test_text)} 字符")
    
    # 创建查询处理器
    processor = ContextGemQueryProcessor()
    
    # 测试查询
    query = "人工智能的主要应用领域有哪些？"
    
    print(f"测试查询: {query}")
    print("=" * 50)
    
    try:
        # 测试分块处理（RAW模式）
        print("开始测试RAW模式分块处理...")
        result = await processor.process_text_query(
            text=test_text,
            query=query,
            language=QueryLanguage.CHINESE,
            extraction_mode=ExtractionMode.RAW,  # 使用RAW模式
            chunk_size=1000,  # 设置较小的分块大小用于测试
            overlap_ratio=0.1,
            enable_deduplication=True
        )
        
        print(f"处理完成！")
        print(f"查询: {result.query}")
        print(f"处理时间: {result.processing_time:.2f}秒")
        print(f"提取项数量: {len(result.extracted_items)}")
        print(f"摘要: {result.summary}")
        print("=" * 50)
        
        # 显示提取的内容
        for i, item in enumerate(result.extracted_items):
            print(f"提取项 {i+1}:")
            print(f"  内容长度: {len(item.content)} 字符")
            print(f"  置信度: {item.confidence}")
            print(f"  源引用: {item.source_reference}")
            if item.metadata:
                print(f"  元数据: {item.metadata}")
            print(f"  内容预览: {item.content[:200]}...")
            print("-" * 30)
            
    except Exception as e:
        print(f"测试失败: {str(e)}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(test_chunking())
