"""
查询处理 API 路由

处理自然语言查询相关的 API 端点
"""

import time
import asyncio
import concurrent.futures
from typing import List, Optional
from fastapi import APIRouter, HTTPException, Depends, Body, UploadFile, File, Form, BackgroundTasks

from app.services.contextgem_query_processor import ContextGemQueryProcessor
from app.services.document_processor import DocumentProcessor
from app.api.models.requests import (
    TextQueryRequest,
    DocumentQueryRequest,
    BatchQueryRequest,
    PipelineConfigRequest
)
from app.api.models.responses import (
    QueryResponse,
    BatchQueryResponse,
    ProcessingStatus
)
from app.core.exceptions import (
    QueryProcessingError,
    ContextGemError,
    DocumentProcessingError
)
from app.utils.logger import app_logger

# 创建路由器
router = APIRouter()

# 服务实例
query_processor = ContextGemQueryProcessor()
document_processor = DocumentProcessor()

# 线程池执行器，用于处理阻塞操作
thread_pool = concurrent.futures.ThreadPoolExecutor(max_workers=4)


@router.post(
    "/query/text",
    response_model=QueryResponse,
    summary="文本查询",
    description="对提供的文本内容进行自然语言查询和信息提取"
)
async def query_text(
    request: TextQueryRequest
) -> QueryResponse:
    """
    对文本内容进行自然语言查询
    
    支持中英文查询，可以提取文本中与查询相关的信息
    
    示例查询：
    - "这个文档的主要内容是什么？"
    - "提取所有的日期和时间信息"
    - "找出文档中提到的人名和公司名"
    - "总结文档的关键要点"
    """
    start_time = time.time()
    
    try:
        app_logger.info(f"开始处理文本查询: {request.query[:50]}...")
        
        # 处理查询 - 使用新的 ContextGem 查询处理器，在线程池中执行
        loop = asyncio.get_event_loop()
        result = await loop.run_in_executor(
            thread_pool,
            lambda: asyncio.run(query_processor.process_text_query(
                text=request.text,
                query=request.query,
                language=request.language,
                extraction_mode=request.extraction_mode
            ))
        )
        
        total_processing_time = time.time() - start_time
        
        # 获取成本信息
        cost_info = query_processor.get_cost_info()
        
        app_logger.info(f"文本查询处理完成，耗时: {total_processing_time:.2f}秒")
        
        return QueryResponse(
            status=ProcessingStatus.SUCCESS,
            message="查询处理成功",
            results=[result],
            total_processing_time=total_processing_time,
            cost_info=cost_info
        )
        
    except QueryProcessingError as e:
        app_logger.error(f"查询处理失败: {str(e)}")
        return QueryResponse(
            status=ProcessingStatus.FAILED,
            message="查询处理失败",
            results=[],
            total_processing_time=time.time() - start_time,
            errors=[str(e)]
        )
        
    except ContextGemError as e:
        app_logger.error(f"ContextGem 处理失败: {str(e)}")
        return QueryResponse(
            status=ProcessingStatus.FAILED,
            message="AI 处理服务异常",
            results=[],
            total_processing_time=time.time() - start_time,
            errors=[str(e)]
        )
        
    except Exception as e:
        app_logger.error(f"文本查询异常: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail="服务器内部错误"
        )


@router.post(
    "/query/document",
    response_model=QueryResponse,
    summary="文档查询",
    description="对已上传的文档进行自然语言查询和信息提取"
)
async def query_document(
    request: DocumentQueryRequest
) -> QueryResponse:
    """
    对已上传的文档进行自然语言查询
    
    需要先通过上传接口上传文档，获得文档ID后进行查询
    
    示例查询：
    - "这份合同的有效期是多久？"
    - "提取所有的财务数据"
    - "找出文档中的风险条款"
    - "总结会议纪要的要点"
    """
    start_time = time.time()
    
    try:
        app_logger.info(f"开始处理文档查询: {request.document_id}, {request.query[:50]}...")
        
        # 获取 ContextGem 文档对象
        try:
            contextgem_doc = await document_processor.get_contextgem_document(request.document_id)
        except DocumentProcessingError as e:
            raise HTTPException(
                status_code=404,
                detail=f"文档不存在: {request.document_id}"
            )

        # 处理查询 - 使用新的 ContextGem 查询处理器，在线程池中执行
        loop = asyncio.get_event_loop()
        result = await loop.run_in_executor(
            thread_pool,
            lambda: asyncio.run(query_processor.process_document_query(
                contextgem_doc=contextgem_doc,
                query=request.query,
                language=request.language,
                extraction_mode=request.extraction_mode
            ))
        )
        
        total_processing_time = time.time() - start_time
        
        # 获取成本信息
        cost_info = query_processor.get_cost_info()
        
        app_logger.info(f"文档查询处理完成，耗时: {total_processing_time:.2f}秒")
        
        return QueryResponse(
            status=ProcessingStatus.SUCCESS,
            message="查询处理成功",
            results=[result],
            total_processing_time=total_processing_time,
            cost_info=cost_info
        )
        
    except HTTPException:
        raise
    except QueryProcessingError as e:
        app_logger.error(f"查询处理失败: {str(e)}")
        return QueryResponse(
            status=ProcessingStatus.FAILED,
            message="查询处理失败",
            results=[],
            total_processing_time=time.time() - start_time,
            errors=[str(e)]
        )
        
    except ContextGemError as e:
        app_logger.error(f"ContextGem 处理失败: {str(e)}")
        return QueryResponse(
            status=ProcessingStatus.FAILED,
            message="AI 处理服务异常",
            results=[],
            total_processing_time=time.time() - start_time,
            errors=[str(e)]
        )
        
    except Exception as e:
        app_logger.error(f"文档查询异常: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail="服务器内部错误"
        )


@router.post(
    "/query/batch",
    response_model=BatchQueryResponse,
    summary="批量查询",
    description="对文本或文档进行批量自然语言查询"
)
async def query_batch(
    request: BatchQueryRequest
) -> BatchQueryResponse:
    """
    批量查询处理
    
    可以一次提交多个查询，对同一份文本或文档进行批量分析
    支持并发处理以提高效率
    
    示例用法：
    - 对一份合同同时提取多个不同类型的信息
    - 对一篇文章进行多角度分析
    - 批量提取结构化数据
    """
    start_time = time.time()
    
    try:
        app_logger.info(f"开始处理批量查询: {len(request.queries)} 个查询")
        
        # 确定查询内容
        if request.document_id and request.text:
            raise HTTPException(
                status_code=400,
                detail="不能同时指定文档ID和文本内容"
            )
        
        if not request.document_id and not request.text:
            raise HTTPException(
                status_code=400,
                detail="必须指定文档ID或文本内容"
            )
        
        # 获取要查询的内容
        if request.document_id:
            try:
                content = await document_processor.get_document_content(request.document_id)
            except DocumentProcessingError as e:
                raise HTTPException(
                    status_code=404,
                    detail=f"文档不存在: {request.document_id}"
                )
        else:
            content = request.text
        
        # 批量处理查询 - 使用新的 ContextGem 查询处理器，在线程池中执行
        loop = asyncio.get_event_loop()
        results = await loop.run_in_executor(
            thread_pool,
            lambda: asyncio.run(query_processor.process_batch_queries(
                text=content,
                queries=request.queries,
                language=request.language,
                extraction_mode=request.extraction_mode
            ))
        )
        
        total_processing_time = time.time() - start_time
        
        # 统计成功和失败的查询
        successful_queries = sum(1 for r in results if r.extracted_items)
        failed_queries = len(results) - successful_queries
        
        # 获取成本信息
        cost_info = query_processor.get_cost_info()
        
        app_logger.info(f"批量查询处理完成，成功: {successful_queries}, 失败: {failed_queries}")
        
        return BatchQueryResponse(
            status=ProcessingStatus.SUCCESS if failed_queries == 0 else ProcessingStatus.PARTIAL,
            message=f"批量查询处理完成，成功 {successful_queries} 个，失败 {failed_queries} 个",
            results=results,
            successful_queries=successful_queries,
            failed_queries=failed_queries,
            total_processing_time=total_processing_time,
            cost_info=cost_info
        )
        
    except HTTPException:
        raise
    except QueryProcessingError as e:
        app_logger.error(f"批量查询处理失败: {str(e)}")
        return BatchQueryResponse(
            status=ProcessingStatus.FAILED,
            message="批量查询处理失败",
            results=[],
            successful_queries=0,
            failed_queries=len(request.queries),
            total_processing_time=time.time() - start_time,
            errors=[str(e)]
        )
        
    except Exception as e:
        app_logger.error(f"批量查询异常: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail="服务器内部错误"
        )


@router.get(
    "/query/examples",
    summary="查询示例",
    description="获取各种类型的查询示例"
)
async def get_query_examples():
    """
    获取查询示例
    
    提供各种类型文档的查询示例，帮助用户了解如何使用查询功能
    """
    examples = {
        "通用查询": [
            "这个文档的主要内容是什么？",
            "总结文档的关键要点",
            "提取文档中的重要信息",
            "这个文档讲了什么？"
        ],
        "信息提取": [
            "提取所有的日期和时间",
            "找出文档中提到的人名",
            "提取所有的数字和金额",
            "找出文档中的地址信息",
            "提取联系方式（电话、邮箱等）"
        ],
        "合同文档": [
            "这份合同的有效期是多久？",
            "合同双方是谁？",
            "提取合同中的重要条款",
            "找出违约责任相关内容",
            "提取付款条件和方式"
        ],
        "财务文档": [
            "提取所有的财务数据",
            "找出收入和支出项目",
            "提取预算信息",
            "找出成本分析相关内容"
        ],
        "会议纪要": [
            "会议的主要议题是什么？",
            "提取会议决议和行动项",
            "找出参会人员名单",
            "总结会议的重要结论"
        ],
        "技术文档": [
            "提取技术规格和参数",
            "找出操作步骤和流程",
            "提取系统要求",
            "找出注意事项和警告"
        ],
        "表格数据": [
            "统计各列的平均值和总和",
            "找出最大值和最小值",
            "按某列分组统计",
            "提取特定条件的数据行",
            "分析数据分布和趋势",
            "计算各列的唯一值数量"
        ],
        "CSV/Excel文件": [
            "这个表格有多少行数据？",
            "列名都有哪些？",
            "销售额最高的是哪一行？",
            "统计各部门的人数",
            "找出年龄大于30的员工",
            "计算平均工资"
        ]
    }
    
    return {
        "message": "查询示例列表",
        "examples": examples,
        "tips": [
            "查询语言支持中文和英文",
            "可以使用自然语言描述要提取的信息",
            "支持复杂的多条件查询",
            "可以指定提取模式：简单、详细、结构化"
        ]
    }


@router.post(
    "/query/upload-and-query",
    response_model=QueryResponse,
    summary="上传文档并查询",
    description="一次性完成文档上传和查询操作，适合临时文档分析"
)
async def upload_and_query(
    file: UploadFile = File(..., description="要上传的文档文件"),
    query: str = Form(..., description="自然语言查询"),
    language: str = Form("auto", description="查询语言"),
    extraction_mode: str = Form("simple", description="提取模式")
) -> QueryResponse:
    """
    上传文档并立即进行查询

    这个接口将文档上传和查询合并为一个操作，适合以下场景：
    - 临时文档分析，不需要保存文档
    - 快速文档查询，一次性操作
    - 批量文档处理脚本

    Args:
        file: 上传的文档文件（支持 DOCX、Excel、CSV、TXT 等格式）
        query: 自然语言查询
        language: 查询语言（auto/zh/en）
        extraction_mode: 提取模式（simple/detailed/structured/raw）

    Returns:
        QueryResponse: 查询结果
    """
    start_time = time.time()

    try:
        app_logger.info(f"开始处理上传并查询: {file.filename}, 查询: {query[:50]}...")

        # 1. 验证文件格式
        if not file.filename:
            raise HTTPException(status_code=400, detail="文件名不能为空")

        file_extension = file.filename.lower().split('.')[-1]
        supported_formats = ['docx', 'xlsx', 'xls', 'csv', 'txt', 'md']

        if file_extension not in supported_formats:
            raise HTTPException(
                status_code=400,
                detail=f"不支持的文件格式: {file_extension}。支持的格式: {', '.join(supported_formats)}"
            )

        # 2. 验证文件大小（50MB 限制，但大文件会使用分块处理）
        file_content = await file.read()
        file_size_mb = len(file_content) / (1024 * 1024)

        if len(file_content) > 50 * 1024 * 1024:  # 50MB
            raise HTTPException(status_code=400, detail="文件大小超过 50MB 限制")

        if file_size_mb > 10:
            app_logger.info(f"处理大文件: {file_size_mb:.1f}MB，将使用分块处理")

        # 3. 处理文档内容
        if file_extension in ['docx', 'xlsx', 'xls', 'csv']:
            # 创建临时文件进行处理
            import tempfile
            import os
            from pathlib import Path

            # 根据文件类型设置正确的后缀
            suffix = f'.{file_extension}'
            with tempfile.NamedTemporaryFile(delete=False, suffix=suffix) as temp_file:
                temp_file.write(file_content)
                temp_file_path = Path(temp_file.name)

            try:
                # 使用 ContextGem 转换器处理文档 - 在线程池中执行
                from app.services.contextgem_converter import ContextGemConverter
                converter = ContextGemConverter()

                # 将阻塞的转换操作移到线程池
                loop = asyncio.get_event_loop()
                if file_extension == 'docx':
                    contextgem_doc = await loop.run_in_executor(
                        thread_pool,
                        lambda: asyncio.run(converter.convert_docx_to_contextgem(temp_file_path))
                    )
                else:  # CSV, Excel 文件
                    contextgem_doc = await loop.run_in_executor(
                        thread_pool,
                        lambda: asyncio.run(converter.convert_file_to_contextgem(temp_file_path))
                    )

                # 使用 ContextGem 查询处理器
                from app.api.models.requests import QueryLanguage, ExtractionMode

                # 转换参数
                lang_map = {"auto": QueryLanguage.AUTO, "zh": QueryLanguage.CHINESE, "en": QueryLanguage.ENGLISH}
                mode_map = {"simple": ExtractionMode.SIMPLE, "detailed": ExtractionMode.DETAILED, "structured": ExtractionMode.STRUCTURED, "raw": ExtractionMode.RAW}

                query_language = lang_map.get(language, QueryLanguage.AUTO)
                query_mode = mode_map.get(extraction_mode, ExtractionMode.SIMPLE)

                # 智能选择处理方式 - 根据文档大小
                doc_size = len(contextgem_doc.raw_text)
                # 保存raw_text至本地
                with open(f"raw_text_{file.filename}.txt", "w", encoding="utf-8") as f:
                    f.write(contextgem_doc.raw_text)
                app_logger.info(f"文档大小: {doc_size} 字符")

                # 设置处理超时 - 为 DOCX 文档增加更长的超时时间
                if file.filename.lower().endswith('.docx'):
                    # DOCX 文档需要更长的处理时间（转换 + 提取）
                    timeout_seconds = 6000 if doc_size > 20000 else 3000  # 大DOCX文档10分钟，小DOCX文档5分钟
                    app_logger.info(f"DOCX 文档超时设置: {timeout_seconds}秒")
                else:
                    # 普通文本文档
                    timeout_seconds = 3000 if doc_size > 20000 else 1200  # 大文档5分钟，小文档2分钟
                    app_logger.info(f"文本文档超时设置: {timeout_seconds}秒")

                try:
                    if doc_size > 15000:  # 大于15K字符使用分块处理
                        app_logger.info("使用大文档分块处理")
                        # 在线程池中执行阻塞的查询操作
                        result = await asyncio.wait_for(
                            loop.run_in_executor(
                                thread_pool,
                                lambda: asyncio.run(query_processor.process_large_document_query(
                                    contextgem_doc=contextgem_doc,
                                    query=query,
                                    language=query_language,
                                    max_chunk_size=65535,
                                    extraction_mode=query_mode,
                                    max_paragraphs_per_call=30,  # ContextGem 官方推荐参数
                                    max_items_per_call=1,        # 最大化并发
                                    use_concurrency=True         # 启用并发处理
                                ))
                            ),
                            timeout=timeout_seconds
                        )
                    else:
                        app_logger.info("使用常规文档处理")
                        # 在线程池中执行阻塞的查询操作
                        result = await asyncio.wait_for(
                            loop.run_in_executor(
                                thread_pool,
                                lambda: asyncio.run(query_processor.process_document_query(
                                    contextgem_doc=contextgem_doc,
                                    query=query,
                                    language=query_language,
                                    extraction_mode=query_mode
                                ))
                            ),
                            timeout=timeout_seconds
                        )
                except asyncio.TimeoutError:
                    raise HTTPException(
                        status_code=408,
                        detail=f"文档处理超时（{timeout_seconds}秒），请尝试较小的文档或简化查询"
                    )
                except Exception as e:
                    if "ContextWindowExceededError" in str(e):
                        # 如果还是上下文超限，尝试更小的块
                        app_logger.warning("上下文窗口超限，尝试更小的分块")
                        try:
                            result = await asyncio.wait_for(
                                loop.run_in_executor(
                                    thread_pool,
                                    lambda: asyncio.run(query_processor.process_large_document_query(
                                        contextgem_doc=contextgem_doc,
                                        query=query,
                                        language=query_language,
                                        extraction_mode=query_mode,
                                        max_paragraphs_per_call=15,  # 更保守的参数
                                        max_items_per_call=1,        # 保持最大并发
                                        use_concurrency=False        # 禁用并发以减少负载
                                    ))
                                ),
                                timeout=timeout_seconds
                            )
                        except Exception as retry_e:
                            raise HTTPException(
                                status_code=413,
                                detail=f"文档过大无法处理，请尝试更小的文档。错误: {str(retry_e)}"
                            )
                    else:
                        raise e

            finally:
                # 清理临时文件
                if temp_file_path.exists():
                    os.unlink(temp_file_path)

        else:
            # 处理其他格式（文本文件等）
            try:
                text_content = file_content.decode('utf-8')
            except UnicodeDecodeError:
                try:
                    text_content = file_content.decode('gbk')
                except UnicodeDecodeError:
                    raise HTTPException(status_code=400, detail="无法解码文件内容，请确保文件编码为 UTF-8 或 GBK")

            # 使用文本查询处理器
            from app.api.models.requests import QueryLanguage, ExtractionMode

            # 转换参数
            lang_map = {"auto": QueryLanguage.AUTO, "zh": QueryLanguage.CHINESE, "en": QueryLanguage.ENGLISH}
            mode_map = {"simple": ExtractionMode.SIMPLE, "detailed": ExtractionMode.DETAILED, "structured": ExtractionMode.STRUCTURED, "raw": ExtractionMode.RAW}

            query_language = lang_map.get(language, QueryLanguage.AUTO)
            query_mode = mode_map.get(extraction_mode, ExtractionMode.SIMPLE)

            # 执行查询 - 在线程池中执行
            loop = asyncio.get_event_loop()
            result = await loop.run_in_executor(
                thread_pool,
                lambda: asyncio.run(query_processor.process_text_query(
                    text=text_content,
                    query=query,
                    language=query_language,
                    extraction_mode=query_mode
                ))
            )

        # 4. 获取成本信息
        cost_info = query_processor.get_cost_info()

        # 5. 计算总处理时间
        total_processing_time = time.time() - start_time

        app_logger.info(f"上传并查询处理完成，总耗时: {total_processing_time:.2f}秒")

        return QueryResponse(
            status="success",
            message="上传并查询处理成功",
            results=[result],
            total_processing_time=total_processing_time,
            cost_info=cost_info,
            errors=[]
        )

    except HTTPException:
        raise
    except Exception as e:
        app_logger.error(f"上传并查询异常: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"上传并查询处理失败: {str(e)}"
        )
