"""
ExtracInfo - 文档信息提取服务主应用

基于 FastAPI 和 ContextGem 的智能文档处理服务
"""

from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
import os
import warnings

from app.core.config import settings
from app.core.exceptions import ExtracInfoException
from app.utils.logger import app_logger

# 抑制 Pydantic 序列化警告（如果配置启用）
if settings.suppress_pydantic_warnings:
    warnings.filterwarnings("ignore", category=UserWarning, module="pydantic")

# 创建 FastAPI 应用实例
app = FastAPI(
    title=settings.app_name,
    version=settings.app_version,
    description="基于 FastAPI 和 ContextGem 的文档信息提取服务，支持多种文档格式和自然语言查询",
    docs_url="/docs",
    redoc_url="/redoc",
    openapi_url="/openapi.json"
)

# 添加 CORS 中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 生产环境中应该限制具体域名
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 创建必要的目录
os.makedirs(settings.upload_dir, exist_ok=True)
os.makedirs(os.path.dirname(settings.log_file), exist_ok=True)


@app.exception_handler(ExtracInfoException)
async def extracinfo_exception_handler(request, exc: ExtracInfoException):
    """处理自定义异常"""
    app_logger.error(f"ExtracInfo异常: {exc.message}", extra={"details": exc.details})
    return JSONResponse(
        status_code=400,
        content={
            "error": exc.message,
            "error_code": exc.error_code,
            "details": exc.details
        }
    )


@app.exception_handler(Exception)
async def general_exception_handler(request, exc: Exception):
    """处理通用异常"""
    app_logger.error(f"未处理的异常: {str(exc)}", exc_info=True)
    return JSONResponse(
        status_code=500,
        content={
            "error": "内部服务器错误",
            "message": "服务暂时不可用，请稍后重试"
        }
    )


@app.get("/")
async def root():
    """根路径 - 服务状态检查"""
    return {
        "service": settings.app_name,
        "version": settings.app_version,
        "status": "running",
        "message": "ExtracInfo 文档信息提取服务正在运行"
    }


@app.get("/health")
async def health_check():
    """健康检查端点"""
    return {
        "status": "healthy",
        "service": settings.app_name,
        "version": settings.app_version
    }


# 导入和注册路由
from app.api.routes import upload, query

app.include_router(
    upload.router,
    prefix=settings.api_v1_prefix,
    tags=["文档上传"],
    responses={404: {"description": "Not found"}}
)

app.include_router(
    query.router,
    prefix=settings.api_v1_prefix,
    tags=["查询处理"],
    responses={404: {"description": "Not found"}}
)


if __name__ == "__main__":
    import uvicorn
    
    app_logger.info(f"启动 {settings.app_name} v{settings.app_version}")
    uvicorn.run(
        "app.main:app",
        host="0.0.0.0",
        port=8090,
        reload=settings.debug,
        log_level=settings.log_level.lower(),
        timeout_keep_alive=30,  # 保持连接超时
        timeout_graceful_shutdown=30,  # 优雅关闭超时
        limit_max_requests=1000,  # 最大请求数
        limit_concurrency=100  # 最大并发数
    )
