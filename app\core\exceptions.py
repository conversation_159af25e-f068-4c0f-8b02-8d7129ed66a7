"""
自定义异常类模块

定义应用中使用的各种自定义异常
"""

from typing import Any, Dict, Optional


class ExtracInfoException(Exception):
    """基础异常类"""
    
    def __init__(
        self,
        message: str,
        error_code: Optional[str] = None,
        details: Optional[Dict[str, Any]] = None
    ):
        self.message = message
        self.error_code = error_code
        self.details = details or {}
        super().__init__(self.message)


class DocumentProcessingError(ExtracInfoException):
    """文档处理异常"""
    pass


class UnsupportedFileTypeError(ExtracInfoException):
    """不支持的文件类型异常"""
    pass


class FileSizeExceededError(ExtracInfoException):
    """文件大小超限异常"""
    pass


class QueryProcessingError(ExtracInfoException):
    """查询处理异常"""
    pass


class ContextGemError(ExtracInfoException):
    """ContextGem 相关异常"""
    pass


class ConfigurationError(ExtracInfoException):
    """配置错误异常"""
    pass
