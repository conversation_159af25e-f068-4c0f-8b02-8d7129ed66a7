"""
文档上传 API 路由

处理文档上传相关的 API 端点
"""

import time
from typing import List, Optional
from fastapi import APIRouter, UploadFile, File, HTTPException, Depends, Query
from fastapi.responses import JSONResponse

from app.services.document_processor import DocumentProcessor
from app.api.models.responses import (
    DocumentUploadResponse, 
    ProcessingStatus, 
    DocumentListResponse,
    DocumentInfo
)
from app.core.exceptions import (
    DocumentProcessingError,
    UnsupportedFileTypeError,
    FileSizeExceededError
)
from app.utils.logger import app_logger

# 创建路由器
router = APIRouter()

# 文档处理器实例
document_processor = DocumentProcessor()


@router.post(
    "/upload",
    response_model=DocumentUploadResponse,
    summary="上传文档",
    description="上传支持的文档格式（DOCX、Excel、CSV、TXT、Markdown）进行处理"
)
async def upload_document(
    file: UploadFile = File(..., description="要上传的文档文件")
) -> DocumentUploadResponse:
    """
    上传并处理文档

    支持的文件格式：
    - DOCX: Microsoft Word 文档
    - XLSX/XLS: Microsoft Excel 文档
    - CSV: 逗号分隔值文件
    - TXT: 纯文本文件
    - MD: Markdown 文件

    返回处理后的文档信息，包括文档ID用于后续查询
    """
    start_time = time.time()
    
    try:
        app_logger.info(f"开始处理上传文件: {file.filename}")
        
        # 检查文件是否为空
        if not file.filename:
            raise HTTPException(
                status_code=400,
                detail="文件名不能为空"
            )
        
        # 处理文档
        document_info = await document_processor.process_uploaded_file(file)
        
        processing_time = time.time() - start_time
        
        app_logger.info(f"文档上传处理完成: {document_info.document_id}")
        
        return DocumentUploadResponse(
            status=ProcessingStatus.SUCCESS,
            message="文档上传并处理成功",
            document_info=document_info,
            processing_time=processing_time
        )
        
    except UnsupportedFileTypeError as e:
        app_logger.warning(f"不支持的文件类型: {str(e)}")
        return DocumentUploadResponse(
            status=ProcessingStatus.FAILED,
            message="文件格式不支持",
            errors=[str(e)],
            processing_time=time.time() - start_time
        )
        
    except FileSizeExceededError as e:
        app_logger.warning(f"文件大小超限: {str(e)}")
        return DocumentUploadResponse(
            status=ProcessingStatus.FAILED,
            message="文件大小超过限制",
            errors=[str(e)],
            processing_time=time.time() - start_time
        )
        
    except DocumentProcessingError as e:
        app_logger.error(f"文档处理失败: {str(e)}")
        return DocumentUploadResponse(
            status=ProcessingStatus.FAILED,
            message="文档处理失败",
            errors=[str(e)],
            processing_time=time.time() - start_time
        )
        
    except Exception as e:
        app_logger.error(f"上传处理异常: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail="服务器内部错误"
        )


@router.post(
    "/upload/batch",
    response_model=List[DocumentUploadResponse],
    summary="批量上传文档",
    description="批量上传多个文档文件进行处理"
)
async def upload_documents_batch(
    files: List[UploadFile] = File(..., description="要上传的文档文件列表")
) -> List[DocumentUploadResponse]:
    """
    批量上传并处理文档
    
    一次可以上传多个文件，每个文件独立处理
    返回每个文件的处理结果
    """
    if len(files) > 10:  # 限制批量上传数量
        raise HTTPException(
            status_code=400,
            detail="批量上传文件数量不能超过10个"
        )
    
    results = []
    
    for file in files:
        try:
            # 为每个文件调用单文件上传处理
            result = await upload_document(file)
            results.append(result)
            
        except HTTPException as e:
            # 处理 HTTP 异常
            results.append(DocumentUploadResponse(
                status=ProcessingStatus.FAILED,
                message=f"文件 {file.filename} 处理失败",
                errors=[e.detail],
                processing_time=0.0
            ))
        except Exception as e:
            # 处理其他异常
            app_logger.error(f"批量上传文件 {file.filename} 处理异常: {str(e)}")
            results.append(DocumentUploadResponse(
                status=ProcessingStatus.FAILED,
                message=f"文件 {file.filename} 处理失败",
                errors=[str(e)],
                processing_time=0.0
            ))
    
    return results


@router.get(
    "/documents",
    response_model=DocumentListResponse,
    summary="获取文档列表",
    description="获取已上传文档的列表信息"
)
async def get_documents(
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(10, ge=1, le=100, description="每页大小")
) -> DocumentListResponse:
    """
    获取文档列表
    
    支持分页查询已上传的文档信息
    """
    try:
        # TODO: 实现文档列表查询
        # 这里需要实现文档元数据的存储和查询
        # 可以使用数据库或文件系统索引
        
        app_logger.info(f"查询文档列表: page={page}, page_size={page_size}")
        
        # 临时返回空列表
        return DocumentListResponse(
            status=ProcessingStatus.SUCCESS,
            message="文档列表查询成功",
            documents=[],
            total_count=0,
            page=page,
            page_size=page_size
        )
        
    except Exception as e:
        app_logger.error(f"查询文档列表失败: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail="查询文档列表失败"
        )


@router.get(
    "/documents/{document_id}",
    response_model=DocumentInfo,
    summary="获取文档信息",
    description="根据文档ID获取具体的文档信息"
)
async def get_document_info(
    document_id: str
) -> DocumentInfo:
    """
    获取指定文档的详细信息
    """
    try:
        # TODO: 实现根据文档ID查询文档信息
        # 这里需要从存储中查询文档元数据
        
        app_logger.info(f"查询文档信息: {document_id}")
        
        # 检查文档是否存在
        try:
            content = await document_processor.get_document_content(document_id)
            # 如果能获取到内容，说明文档存在
            # 但我们需要返回文档信息，这里需要从存储中获取
            # 临时抛出未实现异常
            raise HTTPException(
                status_code=501,
                detail="文档信息查询功能尚未完全实现"
            )
        except DocumentProcessingError:
            raise HTTPException(
                status_code=404,
                detail=f"文档不存在: {document_id}"
            )
        
    except HTTPException:
        raise
    except Exception as e:
        app_logger.error(f"查询文档信息失败: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail="查询文档信息失败"
        )


@router.delete(
    "/documents/{document_id}",
    summary="删除文档",
    description="根据文档ID删除指定的文档"
)
async def delete_document(
    document_id: str
) -> JSONResponse:
    """
    删除指定的文档
    """
    try:
        app_logger.info(f"删除文档: {document_id}")
        
        # 删除文档文件
        success = await document_processor.delete_document(document_id)
        
        if success:
            return JSONResponse(
                status_code=200,
                content={
                    "status": "success",
                    "message": f"文档 {document_id} 删除成功"
                }
            )
        else:
            raise HTTPException(
                status_code=404,
                detail=f"文档不存在: {document_id}"
            )
        
    except HTTPException:
        raise
    except Exception as e:
        app_logger.error(f"删除文档失败: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail="删除文档失败"
        )


@router.get(
    "/documents/{document_id}/content",
    summary="获取文档内容",
    description="获取指定文档的文本内容"
)
async def get_document_content(
    document_id: str
) -> JSONResponse:
    """
    获取文档的文本内容
    """
    try:
        app_logger.info(f"获取文档内容: {document_id}")
        
        # 获取文档内容
        content = await document_processor.get_document_content(document_id)
        
        return JSONResponse(
            status_code=200,
            content={
                "document_id": document_id,
                "content": content,
                "length": len(content)
            }
        )
        
    except DocumentProcessingError as e:
        raise HTTPException(
            status_code=404,
            detail=str(e)
        )
    except Exception as e:
        app_logger.error(f"获取文档内容失败: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail="获取文档内容失败"
        )
