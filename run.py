#!/usr/bin/env python3
"""
ExtracInfo 启动脚本

快速启动 ExtracInfo 文档信息提取服务
"""

import os
import sys
import subprocess
from pathlib import Path


def check_python_version():
    """检查 Python 版本"""
    if sys.version_info < (3, 8):
        print("❌ 错误: 需要 Python 3.8 或更高版本")
        print(f"当前版本: {sys.version}")
        sys.exit(1)
    print(f"✅ Python 版本: {sys.version.split()[0]}")


def check_virtual_env():
    """检查虚拟环境"""
    if hasattr(sys, 'real_prefix') or (hasattr(sys, 'base_prefix') and sys.base_prefix != sys.prefix):
        print("✅ 虚拟环境已激活")
        return True
    else:
        print("⚠️  警告: 未检测到虚拟环境")
        return False


def check_dependencies():
    """检查依赖包"""
    try:
        import fastapi
        import uvicorn
        print("✅ 核心依赖已安装")
        return True
    except ImportError as e:
        print(f"❌ 缺少依赖: {e}")
        print("请运行: pip install -r requirements.txt 或 uv sync")
        return False


def check_env_file():
    """检查环境配置文件"""
    env_file = Path(".env")
    env_example = Path(".env.example")
    
    if env_file.exists():
        print("✅ 环境配置文件存在")
        return True
    elif env_example.exists():
        print("⚠️  .env 文件不存在，但找到 .env.example")
        print("请复制 .env.example 为 .env 并配置相关参数")
        return False
    else:
        print("⚠️  未找到环境配置文件")
        return False


def create_directories():
    """创建必要的目录"""
    dirs = ["uploads", "logs"]
    for dir_name in dirs:
        Path(dir_name).mkdir(exist_ok=True)
    print("✅ 必要目录已创建")


def main():
    """主函数"""
    print("🚀 ExtracInfo 启动检查...")
    print("=" * 50)
    
    # 检查 Python 版本
    check_python_version()
    
    # 检查虚拟环境
    check_virtual_env()
    
    # 检查依赖
    if not check_dependencies():
        sys.exit(1)
    
    # 检查环境配置
    check_env_file()
    
    # 创建必要目录
    create_directories()
    
    print("=" * 50)
    print("🎯 启动 ExtracInfo 服务...")
    
    try:
        # 启动应用
        from app.main import app
        import uvicorn
        
        uvicorn.run(
            "app.main:app",
            host="0.0.0.0",
            port=8090,
            reload=False,
            log_level="info"
        )
    except KeyboardInterrupt:
        print("\n👋 服务已停止")
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()