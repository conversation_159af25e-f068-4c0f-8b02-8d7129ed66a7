"""
应用配置管理模块

管理环境变量、API密钥、模型配置等
"""

import os
from typing import Optional
from pydantic import Field
from pydantic_settings import BaseSettings


class Settings(BaseSettings):
    """应用配置类"""
    
    # 应用基本配置
    app_name: str = Field(default="ExtracInfo", description="应用名称")
    app_version: str = Field(default="0.1.0", description="应用版本")
    debug: bool = Field(default=False, description="调试模式")
    
    # API 配置
    api_v1_prefix: str = Field(default="/api/v1", description="API v1 前缀")
    
    # 文件上传配置
    max_file_size: int = Field(default=50 * 1024 * 1024, description="最大文件大小 (50MB)")
    upload_dir: str = Field(default="uploads", description="文件上传目录")
    allowed_extensions: list[str] = Field(
        default=[".docx", ".xlsx", ".xls", ".csv", ".txt", ".md"],
        description="允许的文件扩展名"
    )
    
    # ContextGem 配置
    contextgem_api_key: Optional[str] = Field(default=None, description="ContextGem API密钥")
    openai_api_key: Optional[str] = Field(default=None, description="OpenAI API密钥")
    openai_base_url: Optional[str] = Field(default=None, description="OpenAI API 基础URL")
    openai_model: str = Field(default="gpt-3.5-turbo", description="OpenAI 模型名称")
    default_model: str = Field(default="openai/gpt-4o-mini", description="默认LLM模型")
    
    # 日志配置
    log_level: str = Field(default="INFO", description="日志级别")
    log_file: str = Field(default="logs/app.log", description="日志文件路径")
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = False


# 全局配置实例
settings = Settings()


def get_settings() -> Settings:
    """获取配置实例"""
    return settings
