[project]
name = "extracinfo"
version = "0.1.0"
description = "基于 FastAPI 和 ContextGem 的文档信息提取服务，支持多种文档格式和自然语言查询"
readme = "README.md"
requires-python = ">=3.11"
dependencies = [
    "fastapi[standard]>=0.115.0",
    "python-multipart>=0.0.6",
    "python-docx>=1.1.0",
    "openpyxl>=3.1.0",
    "aiofiles>=23.2.0",
    "pydantic>=2.5.0",
    "pydantic-settings>=2.0.0",
    "uvicorn[standard]>=0.24.0",
    "python-jose[cryptography]>=3.3.0",
    "passlib[bcrypt]>=1.7.4",
    "aiolimiter>=1.1.0",
    "pandas>=2.0.0",
    "python-magic>=0.4.27",
    "onnxruntime==1.19.2",
    "contextgem>=0.11.1",
    "litellm>=1.0.0",
]

[project.optional-dependencies]
dev = [
    "pytest>=7.4.0",
    "pytest-asyncio>=0.21.0",
    "httpx>=0.25.0",
    "black>=23.0.0",
    "isort>=5.12.0",
    "flake8>=6.0.0",
    "mypy>=1.5.0",
]

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.hatch.build.targets.wheel]
packages = ["app"]

[tool.black]
line-length = 88
target-version = ['py311']

[tool.isort]
profile = "black"
multi_line_output = 3

[tool.mypy]
python_version = "3.11"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
