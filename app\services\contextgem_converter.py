"""
ContextGem 文档转换器

使用官方 ContextGem DocxConverter 将 DOCX 文档转换为 ContextGem Document 对象
同时支持 CSV 和 Excel 文件的转换
"""

import asyncio
from typing import Dict, Any, List, Optional
from pathlib import Path
from contextgem import DocxConverter, Document

from app.core.config import settings
from app.core.exceptions import DocumentProcessingError
from app.utils.logger import app_logger
from app.services.tabular_converter import TabularConverter


class ContextGemConverter:
    """ContextGem 文档转换器"""

    def __init__(self):
        """初始化转换器"""
        self.supported_formats = ['.docx', '.csv', '.xlsx', '.xls']
        # 初始化官方 DocxConverter
        self.docx_converter = DocxConverter()
        # 初始化表格转换器
        self.tabular_converter = TabularConverter()
    
    async def convert_docx_to_contextgem(self, file_path: Path) -> Document:
        """
        将 DOCX 文件转换为 ContextGem Document

        Args:
            file_path: DOCX 文件路径

        Returns:
            Document: ContextGem 文档对象
        """
        try:
            app_logger.info(f"开始转换 DOCX 文件: {file_path}")

            # 使用官方 DocxConverter 进行转换
            contextgem_doc = self.docx_converter.convert(
                str(file_path),
                # 配置转换选项
                apply_markdown=True,  # 应用 Markdown 格式
                include_tables=True,  # 包含表格
                include_headers=True,  # 包含页眉
                include_footers=True,  # 包含页脚
                include_footnotes=True,  # 包含脚注
                include_comments=True,  # 包含评论
                include_links=True,  # 包含链接
                include_textboxes=True,  # 包含文本框
                include_inline_formatting=True,  # 包含内联格式
                include_images=True  # 包含图片
            )

            app_logger.info(f"DOCX 转换完成: {len(contextgem_doc.raw_text)} 字符")
            return contextgem_doc

        except Exception as e:
            app_logger.error(f"DOCX 转换失败: {str(e)}")
            raise DocumentProcessingError(f"DOCX 转换失败: {str(e)}")
    
    def get_text_format(self, file_path: Path, output_format: str = "markdown") -> str:
        """
        获取 DOCX 文件的文本格式

        Args:
            file_path: DOCX 文件路径
            output_format: 输出格式 ("markdown" 或 "raw")

        Returns:
            str: 格式化的文本内容
        """
        try:
            return self.docx_converter.convert_to_text_format(
                str(file_path),
                output_format=output_format
            )
        except Exception as e:
            app_logger.error(f"获取文本格式失败: {str(e)}")
            raise DocumentProcessingError(f"获取文本格式失败: {str(e)}")
    
    async def convert_file_to_contextgem(self, file_path: Path) -> Document:
        """
        根据文件类型转换为 ContextGem Document

        Args:
            file_path: 文件路径

        Returns:
            Document: ContextGem 文档对象
        """
        file_ext = file_path.suffix.lower()

        if file_ext == '.docx':
            return await self.convert_docx_to_contextgem(file_path)
        elif file_ext in ['.csv', '.xlsx', '.xls']:
            return await self.tabular_converter.convert_file_to_contextgem(file_path)
        else:
            raise DocumentProcessingError(f"不支持的文件格式: {file_ext}")

    def get_supported_formats(self) -> List[str]:
        """
        获取支持的文件格式列表

        Returns:
            List[str]: 支持的文件格式列表
        """
        return self.supported_formats.copy()
    
    def is_supported_format(self, file_path: Path) -> bool:
        """
        检查文件格式是否支持

        Args:
            file_path: 文件路径

        Returns:
            bool: 是否支持
        """
        file_ext = file_path.suffix.lower()
        return (file_ext in self.supported_formats or
                self.tabular_converter.is_supported_format(file_path))
